<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <title>Document</title>
</head>

<body>
    <button onclick="sendMessage()">Send</button>
    <script>
        async function sendMessage() {
            try {
                const response = await fetch('/api/v1/messages/send', {
                    method: 'POST',
                    headers: {
                        Authorization: 'Bearer 3|8tbkhuZvqBvDEzgrssa2KKCpGTtxfyc9pIGXfyaf0921793a',
                        Accept: 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        'content': 'test',
                        'chat_id': 1,
                        'type': 'text'
                    }),

                })
                const data = await response.json()
                console.log(data)
            } catch (error) {
                console.log(error)
            }
        }
        window.onload = () => {


            Echo.private(`chat.1`)
                .listen('MessageSent', (e) => {
                    console.log('New message:', e.message);
                });
        }
    </script>
</body>

</html>
