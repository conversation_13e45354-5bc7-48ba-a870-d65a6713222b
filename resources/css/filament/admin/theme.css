@import '/vendor/filament/filament/resources/css/theme.css';

@config './tailwind.config.js';

:root {
    --color-primary-50: 232 240 249;
    --color-primary-100: 209 226 243;
    --color-primary-200: 164 197 231;
    --color-primary-300: 118 168 219;
    --color-primary-400: 73 139 207;
    --color-primary-500: 28 110 195;
    --color-primary-600: 19 73 130;
    --color-primary-700: 14 55 98;
    --color-primary-800: 10 36 65;
    --color-primary-900: 5 18 33;
    --color-primary-950: 2 9 16;
}

/* Enhanced UI Elements - Dark Theme Only */
.fi-sidebar {
    @apply bg-gray-900 bg-gradient-to-b from-gray-800 to-gray-950;
}

.fi-sidebar-header {
    @apply text-white;
}

.fi-sidebar-item {
    @apply text-white/90 font-medium;
}

.fi-sidebar-item-active {
    @apply bg-primary-600/90 text-white;
}

.fi-sidebar-item:hover {
    @apply bg-primary-600/80 text-white transition-all duration-300;
}

.fi-main-ctn {
    @apply bg-gray-900;
}

.fi-btn-primary {
    @apply shadow-md hover:shadow-lg transition-all duration-300;
}

.fi-card {
    @apply bg-gray-800 border-gray-700 shadow-sm hover:shadow-md transition-all duration-300;
}

.fi-ta-header-cell {
    @apply bg-gray-800 text-primary-300 font-medium;
}

.fi-modal-window {
    @apply shadow-xl bg-gray-800 border-gray-700;
}

/* Additional dark theme enhancements */
.fi-form-component-label {
    @apply text-gray-200;
}

.fi-input {
    @apply bg-gray-800 border-gray-700 text-white;
}

.fi-select-input {
    @apply bg-gray-800 border-gray-700 text-white;
}

.fi-tabs-item {
    @apply text-gray-300 hover:text-white;
}

.fi-tabs-item-active {
    @apply text-primary-400 border-primary-500;
}

.fi-section {
    @apply border-gray-700;
}