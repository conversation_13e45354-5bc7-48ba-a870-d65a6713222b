import preset from '../../../../vendor/filament/filament/tailwind.config.preset'

export default {
    presets: [preset],
    content: [
        './app/Filament/Resources/**/*.php',
        './app/Filament/Pages/**/*.php',
        './resources/views/filament/admin/**/*.blade.php',
        './vendor/filament/**/*.blade.php',
    ],
    theme: {
        extend: {
            colors: {
                primary: {
                    '50': 'rgb(var(--color-primary-50) / <alpha-value>)',
                    '100': 'rgb(var(--color-primary-100) / <alpha-value>)',
                    '200': 'rgb(var(--color-primary-200) / <alpha-value>)',
                    '300': 'rgb(var(--color-primary-300) / <alpha-value>)',
                    '400': 'rgb(var(--color-primary-400) / <alpha-value>)',
                    '500': 'rgb(var(--color-primary-500) / <alpha-value>)',
                    '600': 'rgb(var(--color-primary-600) / <alpha-value>)',
                    '700': 'rgb(var(--color-primary-700) / <alpha-value>)',
                    '800': 'rgb(var(--color-primary-800) / <alpha-value>)',
                    '900': 'rgb(var(--color-primary-900) / <alpha-value>)',
                    '950': 'rgb(var(--color-primary-950) / <alpha-value>)',
                },
            },
        },
    },
}
