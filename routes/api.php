<?php

use App\Http\Controllers\api\v1\AchievementController;
use App\Http\Controllers\api\v1\BannerController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\api\v1\ChatController;
use App\Http\Controllers\api\v1\MessageController;
use App\Http\Controllers\api\v1\ProfileController;
use App\Http\Middleware\SetUserTimezone;
use App\Http\Controllers\api\v1\AuthController;
use App\Http\Controllers\api\v1\EventController;
use App\Http\Controllers\api\v1\ExpenseCategoryController;
use App\Http\Controllers\api\v1\ExpenseController;
use App\Http\Controllers\api\v1\TaskController;
use App\Http\Controllers\api\v1\AlbumController;
use App\Http\Controllers\api\v1\AreaController;
use App\Http\Controllers\api\v1\CallLogController;
use App\Http\Controllers\api\v1\CallsController;
use App\Http\Controllers\api\v1\FamilyController;
use App\Http\Controllers\api\v1\LocationController;
use App\Http\Controllers\api\v1\MediaController;
use App\Http\Controllers\api\v1\NotificationController;
use App\Http\Controllers\api\v1\OccasionController;
use App\Http\Controllers\api\v1\PollController;
use App\Http\Controllers\api\v1\ShoppingListController;
use App\Http\Controllers\api\v1\StorageOptimizationController;
use App\Http\Controllers\api\v1\SubTaskController;
use App\Http\Controllers\api\v1\UserStatusController;

Route::prefix('v1')->group(function () {

    // Public routes

    Route::post('login', [AuthController::class, 'login'])->name('login');
    Route::post('register', [AuthController::class, 'register']);
    Route::post('forgot-password', [AuthController::class, 'forgetPassword']);
    Route::post('check-code', [AuthController::class, 'checkCode']);
    Route::post('reset-password', [AuthController::class, 'resetPassword']);
    Route::middleware(['auth:sanctum', SetUserTimezone::class])->group(function () {
        Route::get('banners', BannerController::class);
        Route::post('calls/generate-token', [CallsController::class, 'generateToken']);
        Route::post('register-family', [AuthController::class, 'registerFamily']);
        Route::post('archive-chat/{id}', [ChatController::class, 'archive']);
        Route::apiResource('chats', ChatController::class);
        Route::apiResource('events', EventController::class)->except('update');
        Route::post('events/{id}', [EventController::class, 'update']);
        Route::get('budget', [ExpenseController::class, 'home']);
        Route::apiResource('expenses', ExpenseController::class);
        Route::apiResource('expense-categories', ExpenseCategoryController::class);
        Route::apiResource('tasks', TaskController::class)->except('update');
        Route::post('tasks/{id}', [TaskController::class, 'update']);
        Route::post('subTasks/sort', [SubTaskController::class, 'sort']);
        Route::apiResource('subTasks', SubTaskController::class);
        Route::post('invite/email', [AuthController::class, 'inviteByEmail']);
        Route::post('invite/multiple-emails', [AuthController::class, 'inviteMultipleByEmail']);
        Route::post('invite', [FamilyController::class, 'invite']);
        Route::post('shopping-lists/check/{id}', [ShoppingListController::class, 'checkItem']);
        Route::apiResource('shopping-lists', ShoppingListController::class)->except('update');
        Route::post('shopping-lists/{id}', [ShoppingListController::class, 'update']);
        Route::get('achievements', AchievementController::class);
        Route::prefix('profile')->group(function () {
            Route::get('/', [ProfileController::class, 'show'])->name('profile.show');
            Route::put('/', [ProfileController::class, 'update'])->name('profile.update');
            Route::post('/update-image', [ProfileController::class, 'updateProfilePicture'])->name('profile.updateImage');
        });
        Route::post('polls/vote', [PollController::class, 'vote']);
        Route::apiResource('polls', PollController::class);

        Route::apiResource('albums', AlbumController::class);
        Route::apiResource('media', MediaController::class)->except('destroy');
        Route::delete('media', [MediaController::class, 'destroy']);

        Route::prefix('storage')->group(function () {
            Route::get('/status/{family}', [StorageOptimizationController::class, 'getStatus']);
            Route::get('/analyze/{family}', [StorageOptimizationController::class, 'analyze']);
            Route::get('/results/{family}', [StorageOptimizationController::class, 'getResults']);
        });
        Route::post('messages/send', [MessageController::class, 'store']);
        Route::get('read-all/{chat}', [MessageController::class, 'readAll']);
        Route::post('call/accept/{chat}', [CallsController::class, 'accept']);
        Route::post('call/end/{chat}', [CallsController::class, 'endCall']);
        Route::post('call/{chat}', [MessageController::class, 'call']);
        Route::apiResource('messages', MessageController::class);
        Route::get('members', [FamilyController::class, 'members']);
        Route::apiResource('occasions', OccasionController::class);
        Route::apiResource('call-logs', CallLogController::class);
        Route::get('notifications', NotificationController::class);
        Route::post('update-location', [LocationController::class, 'update']);
        Route::post('stop-location', [LocationController::class, 'stopStreaming']);
        Route::post('enter-area', [AreaController::class, 'enterArea']);
        Route::apiResource('areas', AreaController::class);

        // User status routes
        Route::prefix('user-status')->group(function () {
            Route::get('/{userId}', [UserStatusController::class, 'show']);
            Route::post('/batch', [UserStatusController::class, 'batchStatus']);
            Route::get('/family', [UserStatusController::class, 'familyMembers']);
        });
    });
});
