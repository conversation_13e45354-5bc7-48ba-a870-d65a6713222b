<?php

use App\Models\Area;
use App\Models\Chat;
use App\Models\Event;
use App\Models\Family;
use App\Models\User;
use Illuminate\Broadcasting\BroadcastEvent;
use Illuminate\Broadcasting\Channel;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Event as FacadesEvent;

Broadcast::channel('chat.{chat}', fn($user, Chat $chat) => $chat->members->contains($user));
Broadcast::channel('messages.{id}', fn($user, $id) => $user->id == $id);
Broadcast::channel('calls.{id}', fn(User $user, $id) => $user->id == $id);
Broadcast::channel('event.{event}', fn($user, Event $event) => $event->family->users->contains($user));
Broadcast::channel('location.{family}', fn($user, Family $family) => $family->users->contains($user));
Broadcast::channel('user.{user}', fn($user, User $usr) => $user->id == $usr->id);
Broadcast::channel('family.{family}', fn($user, Family $family) => $family->users->contains($user));
Broadcast::channel('public_notification', fn() => true);
Broadcast::channel('presence-family.{family}', fn($user, $family) => $family->users->contains($user));
Broadcast::channel('presence-user.{id}', function ($user, $id) {
    if ($user->id == $id) {
        return [
            'id' => $user->id,
            'name' => $user->name,
        ];
    }
});

// Area-specific channels for broadcasting area events
Broadcast::channel('area.{area}', function ($user, Area $area) {
    // User can listen to area events if they belong to the area's family
    return $area->family->users->contains($user);
});

