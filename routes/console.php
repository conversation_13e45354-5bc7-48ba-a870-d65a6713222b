<?php

use App\Jobs\EventReminderJob;
use App\Jobs\AnalyzeStorageJob;
use App\Jobs\ApproachingTasksJob;
use App\Jobs\RecurringEventJob;
use App\Jobs\RecurringExpensesJob;
use App\Jobs\UpcomingExpensesJob;
use Illuminate\Support\Facades\Schedule;


Schedule::call(fn() => EventReminderJob::dispatch())->everyMinute();
Schedule::call(fn() => RecurringEventJob::dispatch())->daily();
Schedule::call(fn() => UpcomingExpensesJob::dispatch())->everyMinute();
Schedule::call(fn() => AnalyzeStorageJob::dispatch())->monthly();
Schedule::call(fn() => RecurringExpensesJob::dispatch())->hourly();
Schedule::call(fn() => ApproachingTasksJob::dispatch())->everyMinute();
