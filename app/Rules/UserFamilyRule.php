<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UserFamilyRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = request()->user();
        if (!$user->families()->whereHas('users', fn($q) => $q->where('users.id', $value))->exists()) {
            $fail(__('This user is not memeber of your family.'));
        }
    }
}
