<?php

namespace App\Rules;

use App\Models\Album;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class FamilyAlbumRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $check = Album::whereHas('family', fn($query) => $query->whereHas('users', fn($query) => $query->where('user_id', auth()->id())))
            ->where('id', $value)
            ->exists();
        if (!$check) {

            $fail(__("The album id is invalid"));
        }
    }
}
