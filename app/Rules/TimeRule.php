<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class TimeRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $pattern = '/^(\d+d)?(\d+h)?(\d+m)?$/';

        // Ensure the value matches the pattern
        if (!preg_match($pattern, $value)) {
            $fail(__(":attribute must be in a valid time format (e.g., 1d, 2h, 30m, 1d2h30m)."));
        }
    }
}
