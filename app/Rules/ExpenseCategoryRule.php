<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ExpenseCategoryRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!auth()->user()->families()->whereHas('expenseCategories', fn($q) => $q->where('expense_categories.id', '=', $value))->exists()) {
            $fail(__('The selected expense category is invalid.'));
        }
    }
}
