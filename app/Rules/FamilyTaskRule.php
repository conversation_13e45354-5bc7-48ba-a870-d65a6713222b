<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class FamilyTaskRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!auth()->user()->families()->whereHas('tasks', fn($q) => $q->where('tasks.id', $value))->exists()) {
            $fail(__("the Task id is no valid"));
        }
    }
}
