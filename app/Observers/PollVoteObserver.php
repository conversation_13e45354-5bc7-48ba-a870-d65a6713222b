<?php

namespace App\Observers;

use App\Models\PollItem;
use App\Models\PollVote;
use Illuminate\Support\Facades\DB;

class PollVoteObserver
{
    /**
     * Handle the PollVote "created" event.
     */
    public function created(PollVote $pollVote): void
    {
        PollItem::query()
            ->where("id", $pollVote->poll_item_id)
            ->update([
                'total_votes' => DB::raw('total_votes + 1')
            ]);
    }

    /**
     * Handle the PollVote "updated" event.
     */
    public function updated(PollVote $pollVote): void
    {
        if ($pollVote->isDirty('poll_item_id')) {
            // If the poll_item_id has changed, decrement the old item's vote count
            $oldItemId = $pollVote->getOriginal('poll_item_id');
            if ($oldItemId) {
                PollItem::query()
                    ->where("id", $oldItemId)
                    ->update([
                        'total_votes' => DB::raw('total_votes - 1')
                    ]);
            }

            // Increment the new item's vote count
            PollItem::query()
                ->where("id", $pollVote->poll_item_id)
                ->update([
                    'total_votes' => DB::raw('total_votes + 1')
                ]);
        }
    }

    /**
     * Handle the PollVote "deleted" event.
     */
    public function deleted(PollVote $pollVote): void
    {
        // Decrement the vote count for the poll item when a vote is deleted
        PollItem::query()
            ->where("id", $pollVote->poll_item_id)
            ->update([
                'total_votes' => DB::raw('total_votes - 1')
            ]);
    }

    /**
     * Handle the PollVote "restored" event.
     */
    public function restored(PollVote $pollVote): void
    {
        //
    }

    /**
     * Handle the PollVote "force deleted" event.
     */
    public function forceDeleted(PollVote $pollVote): void
    {
        //
    }
}
