<?php

namespace App\Tables\Columns;

use App\Enums\TaskStatus;
use Filament\Tables\Columns\Column;

class ShoppingListProgress extends Column
{
    protected string $view = 'tables.columns.shopping-list-progress';


    public function getProgress(): int
    {
        $items = $this->record->items()->get();
        $purchased = $items->where('is_purchased', true)->count();
        $total = $items->count();

        if ($total == 0) return 0;

        $progress = $purchased * 100 / $total;
        return $progress;
    }
}
