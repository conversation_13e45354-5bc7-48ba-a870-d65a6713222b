<?php

namespace App\Tables\Columns;

use App\Enums\TaskStatus;
use Filament\Tables\Columns\Column;

class TaskProgress extends Column
{
    protected string $view = 'tables.columns.task-progress';


    public function getProgress(): int
    {
        $subtasks = $this->record->subTasks()->get();
        $done = $subtasks->where('status', TaskStatus::DONE)->count();
        $total = $subtasks->count();

        if ($total == 0) return 0;

        $progress = $done * 100 / $total;
        return $progress;
    }
}
