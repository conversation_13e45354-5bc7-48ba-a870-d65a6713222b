<?php

namespace App\Services;

use App\Models\Chat;
use App\Services\Service;
use Http\Discovery\Exception\NotFoundException;

class ChatService extends Service
{

    public function model()
    {
        return Chat::class;
    }
    public function getUserChats(string $userId, ?bool $archived = false, $search = null)
    {
        return $this->model::query()
            ->when($search, fn($query) => $query->where('name', 'like', "$search%"))
            ->whereHas('members', fn($q) => $q->where('user_id', '!=', $userId))
            ->whereHas('members', fn($q) => $q
                ->where('user_id', $userId)
                ->where('chat_members.is_archived', $archived))

            ->with([
                'members',
                'image',
                'createdBy',
                'lastMessage',
                'family'
            ])
            ->orderBy('updated_at', 'desc')
            ->paginate(50);
    }
    public function getChatDetails(string $chatId, ?string $userId)
    {
        return $this->model::query()
            ->with(['members', 'family', 'createdBy', 'messages' => fn($q) => $q->limit(10)])
            ->when($userId, fn($q) => $q->whereHas('members', fn($q) => $q->where('users.id', $userId)))
            ->findOrFail($chatId);
    }
    public function archive($user, $chatId)
    {
        $chat = $user->chats()->where("chat_id", $chatId)->first();
        if (!$chat) {
            throw new NotFoundException(__("Chat not found"));
        }
        $chat->pivot->is_archived = !$chat->pivot->is_archived;
        $chat->pivot->save();
        return $chat;
    }
}
