<?php

namespace App\Services;

use App\Models\Album;
use App\Services\Service;
use Illuminate\Support\Facades\DB;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class MediaService extends Service
{

    public function model()
    {
        return Media::class;
    }
    public function create(array $data)
    {
        $album = Album::find($data['album_id']);
        return $album->addMedia($data['image'])->toMediaCollection('gallery');

    }
    public function all(?int $paginate = null, array $with = [], array $where = [], bool $getAsQuery = false)
    {
        $query = Album::query()
            ->where('family_id', $where['family_id'])
            ->with('gallery')
            ->paginate($paginate);
        $media = [];
        foreach ($query as $item) {
            $media = [...$media, ...$item->gallery];
        }
        return $media;
    }
    public function delete(array|int $ids)
    {
        DB::table('media')
            ->whereIn('media.id', $ids)
            ->join('albums', 'albums.id', '=', 'media.model_id')
            ->join('families', 'families.id', '=', 'albums.family_id')
            ->where('media.model_type', Album::class)
            ->whereIn('families.id', auth()->user()->families()->pluck('id'))
            ->whereIn('media.id', $ids)
            ->delete();
    }
}
