<?php

namespace App\Services;

use App\Models\VerificationCode;
use App\Notifications\VerificationCodeNotification;
use App\Services\Service;

class VerificationCodeService extends Service
{

    public function model()
    {
        return VerificationCode::class;
    }
    public function generate($user)
    {
        $code = mt_rand(1000, 9999);
        $codes = VerificationCode::where('code', 'like', $code . '%')->pluck('code');
        do {

            $code = mt_rand(1000, 9999);
        } while ($codes->contains($code));
        VerificationCode::query()->updateOrCreate([
            'user_id' => $user->id
        ], ['code' => $code]);
        $user->notify(new VerificationCodeNotification(
            __("Forgot password"),
            __("Your passord reset code is"),
            $code
        ));
        return $code;
    }
}
