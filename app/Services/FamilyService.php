<?php

namespace App\Services;

use App\Models\Family;
use App\Models\SubscriptionPlan;
use App\Models\User;
use App\Services\Service;

class FamilyService extends Service
{

    public function model()
    {
        return Family::class;
    }

    public function create(array $data)
    {
        $family = $this->model()::create($data);
        $plan = SubscriptionPlan::where('is_default', true)->first();
        if ($plan) {
            $family->subscriptions()->create([
                'subscription_plan_id' => $plan->id,
                'storage_limit' => $plan->storage_limit
            ]);
        }
        return $family;
    }
    public function getFamilyMembers(string $familyId, bool $getAsQuery = false)
    {
        $query = User::query()
            ->whereHas('families', fn($q) => $q->where('id', $familyId));
        return $getAsQuery ? $query : $query->get();
    }
    public function getSharedMembers($userId)
    {
        return User::query()
            ->whereHas('families', fn($query) => $query->whereHas('users', fn($query) => $query->where('users.id', $userId)))
            ->where('id', '!=', $userId)
            ->get();
    }
}
