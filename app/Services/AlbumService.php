<?php

namespace App\Services;

use App\Models\Album;
use App\Services\Service;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class AlbumService extends Service
{

    public function model()
    {
        return Album::class;
    }

    public function create(array $data)
    {
        return DB::transaction(function () use ($data) {
            $assignedUserIds = $data['assigned_user_ids'] ?? [];
            unset($data['assigned_user_ids']);

            $album = parent::create($data);

            // Create album assignments
            if (!empty($assignedUserIds)) {
                $assignmentsData = [];
                foreach ($assignedUserIds as $userId) {
                    $assignmentsData[] = ['user_id' => $userId];
                }
                $album->assignments()->createMany($assignmentsData);
            }

            $album->load(['assignments', 'assignedUsers']);
            return $album;
        });
    }

    public function update(array $data, string $id)
    {
        return DB::transaction(function () use ($data, $id) {
            $assignedUserIds = $data['assigned_user_ids'] ?? null;
            unset($data['assigned_user_ids']);

            $album = parent::update($data, $id);

            // Update album assignments if provided
            if ($assignedUserIds !== null) {
                $album->assignments()->delete();
                if (!empty($assignedUserIds)) {
                    $assignmentsData = [];
                    foreach ($assignedUserIds as $userId) {
                        $assignmentsData[] = ['user_id' => $userId];
                    }
                    $album->assignments()->createMany($assignmentsData);
                }
            }

            $album->load(['assignments', 'assignedUsers']);
            return $album;
        });
    }
}
