<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;

class GenerateFileHash
{
    /**
     * Generates a SHA-256 hash for the specified file.
     *
     * @param string $filePath The relative path to the file.
     * @return string|null The SHA-256 hash of the file if it exists, otherwise null.
     */

    public static function generate($filePath): ?string
    {
        $fullPath = Storage::disk('public')->path($filePath);

        if (Storage::disk('public')->exists($filePath)) {
            return hash_file('sha256', $fullPath);
        }

        return null;
    }
}
