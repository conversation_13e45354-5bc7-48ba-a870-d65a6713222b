<?php

namespace App\Services;

use App\Models\ShoppingList;
use App\Services\Service;

class ShoppingListService extends Service
{

    public function model()
    {
        return ShoppingList::class;
    }
    public function all(int|null $paginate = null, array $with = [], array $where = [], bool $getAsQuery = false)
    {
        if (isset($where['group_by'])) {
            unset($where['group_by']);
            return ShoppingList::query()
                ->with($with)
                ->where($where)
                ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
                ->orderBy('created_at', 'desc')
                ->get();
        }

        return parent::all($paginate, $with, $where);
    }
    public function create(array $data)
    {
        $items = [];
        if (isset($data['items'])) {
            foreach ($data['items'] as $item) {
                $items[] = ['name' => $item];
            }
            unset($data['items']);
        }

        $record = parent::create($data);
        $record->items()->createMany($items);
        return $record;
    }
}
