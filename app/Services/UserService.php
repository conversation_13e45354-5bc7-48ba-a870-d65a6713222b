<?php

namespace App\Services;

use App\Models\User;
use App\Services\Service;

class UserService extends Service
{

    public function model()
    {
        return User::class;
    }
    public function create(array $data)
    {
        $image = null;
        if (isset($data['image'])) {

            $image = $data['image'];
            unset($data['image']);
        }
        $user = parent::create($data);
        if ($image) {
            $user->addMedia($image)->toMediaCollection('image');
        }
        // $user->assignRole('family_member');
        return $user;
    }
}
