<?php

namespace App\Services;

use App\Services\Service;
use Filament\Notifications\Notification;

class NotificationService extends Service
{

    public function model()
    {
        return Notification::class;
    }
    public function getUserNotificaitons($user_id)
    {
        return $this->model::query()
            ->where('notifiable_type', \App\Models\User::class)
            ->where('notifiable_id', $user_id)
            ->orderBy('created_at', 'desc')
            ->paginate();
    }
}
