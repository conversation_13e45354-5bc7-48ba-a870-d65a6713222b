<?php

namespace App\Services;

use Closure;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Model;

abstract class Service
{
    protected $model;

    protected $whereHases = [];
    protected $wheres = [];
    protected $whereDates = [];

    protected $orderBy = [];

    public function __construct()
    {
        $this->setModel();
    }
    public function setModel()
    {
        $model = $this->model();
        if (is_subclass_of($model, Model::class)) {
            $this->model = $model;
        } else {
            throw new BindingResolutionException('the class ' . $model . ' is not subclass of class Model');
        }
    }
    /**
     * abstract function to set the model to be used
     */
    abstract function model();

    /**
     * added whereHas condition
     */
    public function whereHas(string $relation, ?Closure $callback = null)
    {
        $this->whereHases[] = compact('relation', 'callback');
        return $this;
    }
    public function orderBy(string $column, string $order = 'asc')
    {
        $this->orderBy[] = compact('column', 'order');
        return $this;
    }
    public function where(string $column, $value, string $operator = '=')
    {
        $this->wheres[] = compact('column', 'operator', 'value');
        return $this;
    }
    public function whereDate(string $column, string $value, string $operator = '=')
    {
        $this->whereDates[] = compact('column', 'value', 'operator');
        return $this;
    }
    public function whereIn(string $column, array $values)
    {
        $this->wheres[] = [
            'column' => $column,
            'operator' => 'in',
            'value' => $values,
        ];
        return $this;
    }

    /**
     * get list of all records
     *
     */
    public function all(?int $paginate = null, array $with = [], array $where = [], bool $getAsQuery = false)
    {
        $query = $this->model::query();
        if (!empty($with)) {
            $query = $query->with($with);
        }

        if (!empty($where)) {
            foreach ($where as $key => $value) {
                $query = $query->where($key, $value);
            }
        }
        $query = $this->prepareWhereHas($query);
        $query = $this->prepareWhere($query);
        $query = $this->prepareOrderBy($query);
        $query = $this->prepareWhereDate($query);
        if ($getAsQuery)
            return $query;

        if ($paginate)
            return $query->paginate($paginate);
        return $query->get();
    }
    /**
     * get single item
     * @param string $id
     * @return mixed
     */
    public function show(string $id, array $with = [], array $where = [])
    {
        $query = $this->model::query()->where($where);
        if (!empty($with)) {
            $query = $query->with($with);
        }
        $query = $this->prepareWhereHas($query);
        $query = $this->prepareOrderBy($query);
        $query = $this->prepareWhere($query);
        $result = $query->find($id);
        return $result;
    }
    /**
     * get single item
     */
    public function first(array $with = [], array $where = [])
    {
        $query = $this->model::query()->where($where);
        if (!empty($with)) {
            $query = $query->with($with);
        }
        $query = $this->prepareWhereHas($query);
        $query = $this->prepareWhere($query);
        $query = $this->prepareWhereDate($query);
        $query = $this->prepareOrderBy($query);
        $result = $query->first();
        return $result;
    }
    public function count()
    {
        $query = $this->model::query();
        $query = $this->prepareWhereHas($query);
        $query = $this->prepareWhere($query);
        $query = $this->prepareWhereDate($query);
        $query = $this->prepareOrderBy($query);
        $result = $query->count('id');
        return $result;
    }
    public function firstOrfail(array $with = [], array $where = [])
    {
        $query = $this->model::query()->where($where);
        if (!empty($with)) {
            $query = $query->with($with);
        }
        $query = $this->prepareWhereHas($query);
        $query = $this->prepareWhereDate($query);
        $query = $this->prepareWhere($query);
        $query = $this->prepareOrderBy($query);
        $result = $query->firstOrfail();
        return $result;
    }
    /**
     * create new record
     * @param array $data
     * @return mixed
     */
    public function create(array $data)
    {
        $result = $this->model::create($data);
        return $result;
    }
    /**
     * update record
     * @param array $data
     * @param int $id
     * @return mixed
     */
    public function update(array $data, string $id)
    {
        $query = $this->model::query();

        $query = $this->prepareWhereHas($query);
        $query = $this->prepareWhere($query);
        $item = $query->findOrFail($id);
        $item->update($data);
        return $item;
    }

    /**
     * update multiple records
     * @param array $data
     * @return int The number of affected rows
     */
    public function updateMultiple(array $data)
    {
        $query = $this->model::query();

        $query = $this->prepareWhereHas($query);
        $query = $this->prepareWhere($query);

        return $query->update($data);
    }

    /**
     * delete records or record
     * @param int $id
     * @return void
     */
    public function delete(array|int $ids)
    {
        $query = $this->model::query();
        $query = $this->prepareWhereHas($query);
        $query = $this->prepareWhere($query);
        return $query->whereIn('id', [...$ids])->delete();
    }


    /**
     * check if record exists
     */
    public function exists(array $where = [])
    {
        $query = $this->model::query();

        $query = $this->prepareWhereHas($query);
        $query = $this->prepareWhere($query);
        return $query->where($where)->exists();
    }
    /**
     * add the whereHas to a query
     */
    protected function prepareWhereHas($query)
    {
        if (!empty($this->whereHases)) {
            foreach ($this->whereHases as $item) {
                $query = $query->whereHas($item['relation'], $item['callback']);
            }
        }
        return $query;
    }
    /**
     * add the wheres to a query
     */
    protected function prepareWhere($query): mixed
    {
        if (!empty($this->wheres)) {
            foreach ($this->wheres as $item) {
                if ($item['operator'] === 'in') {
                    $query = $query->whereIn($item['column'], $item['value']);
                } else {
                    $query = $query->where($item['column'], $item['operator'], $item['value']);
                }
            }
        }
        return $query;
    }
    protected function prepareOrderBy($query): mixed
    {
        if (!empty($this->orderBy)) {
            foreach ($this->orderBy as $item) {
                $query = $query->orderBy($item['column'], $item['order']);
            }
        }
        return $query;
    }

    /**
     * Prepare whereDate conditions
     */
    protected function prepareWhereDate($query)
    {
        if (!empty($this->whereDates)) {
            foreach ($this->whereDates as $item) {
                $query = $query->whereDate($item['column'], $item['operator'], $item['value']);
            }
        }
        return $query;
    }
}
