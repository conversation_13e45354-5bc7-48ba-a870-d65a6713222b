<?php

namespace App\Services;

use App\Models\CallLog;
use App\Services\Service;

class CallLogService extends Service
{

    public function model()
    {
        return CallLog::class;
    }

    public function userLog($userId)
    {
        return $this->model::query()
            ->with(['user', 'callUsers.user', 'chat'])
            ->where("user_id", $userId)
            ->orWhereHas('callUsers', fn($query) => $query->where('call_users.user_id', $userId))
            ->orderBy('call_logs.created_at', 'desc')
            ->paginate(20);
    }
}
