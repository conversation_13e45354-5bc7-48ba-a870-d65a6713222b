<?php

namespace App\Services;

use App\Models\Poll;
use App\Models\PollItem;
use App\Models\PollVote;
use App\Services\Service;
use Illuminate\Support\Facades\DB;

class PollService extends Service
{

    public function model()
    {
        return Poll::class;
    }
    public function create(array $data)
    {
        return DB::transaction(function () use ($data) {
            $items = $data['items'];
            $assignedUserIds = $data['assigned_user_ids'] ?? [];
            unset($data['items'], $data['assigned_user_ids']);

            $poll = parent::create($data);

            // Create poll items
            $itemsData = [];
            foreach ($items as $item) {
                $itemsData[] = ['name' => $item];
            }
            $poll->items()->createMany($itemsData);

            // Create poll assignments
            if (!empty($assignedUserIds)) {
                $assignmentsData = [];
                foreach ($assignedUserIds as $userId) {
                    $assignmentsData[] = ['user_id' => $userId];
                }
                $poll->assignments()->createMany($assignmentsData);
            }

            $poll->load(['items', 'assignments', 'assignedUsers']);
            return $poll;
        });
    }
    public function update(array $data, $id)
    {
        return DB::transaction(function () use ($data, $id) {
            $items = $data['items'] ?? null;
            $assignedUserIds = $data['assigned_user_ids'] ?? null;
            unset($data['items'], $data['assigned_user_ids']);

            $poll = parent::update($data, $id);

            // Update poll items if provided
            if ($items !== null) {
                $poll->items()->delete();
                $itemsData = [];
                foreach ($items as $item) {
                    $itemsData[] = ['name' => $item];
                }
                $poll->items()->createMany($itemsData);
            }

            // Update poll assignments if provided
            if ($assignedUserIds !== null) {
                $poll->assignments()->delete();
                if (!empty($assignedUserIds)) {
                    $assignmentsData = [];
                    foreach ($assignedUserIds as $userId) {
                        $assignmentsData[] = ['user_id' => $userId];
                    }
                    $poll->assignments()->createMany($assignmentsData);
                }
            }

            $poll->load(['items', 'assignments', 'assignedUsers']);
            return $poll;
        });
    }
    public function vote(string $pollId, string $itemId, string $userId)
    {
        return DB::transaction(function () use ($pollId, $itemId, $userId) {
            // First, delete any existing votes for this user on this poll
            $existingVote = PollVote::query()
                ->where('poll_id', '=', $pollId)
                ->where('user_id', '=', $userId)
                ->first();

            if ($existingVote) {
                // If the user is voting for the same item, no need to change anything
                if ($existingVote->poll_item_id == $itemId) {
                    return $existingVote;
                }

                // Delete the existing vote - the observer will handle decrementing the count
                $existingVote->delete();
            }

            // Create a new vote - the observer will handle incrementing the count
            return PollVote::create([
                'poll_id' => $pollId,
                'user_id' => $userId,
                'poll_item_id' => $itemId
            ]);
        });
    }
}
