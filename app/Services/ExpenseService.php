<?php

namespace App\Services;

use App\Enums\ExpenseType;
use App\Models\Expense;
use App\Services\Service;
use Illuminate\Support\Facades\DB;

class ExpenseService extends Service
{

    public function model()
    {
        return Expense::class;
    }
    public function all(int|null $paginate = null, array $with = [], array $where = [], bool $getAsQuery = false)
    {
        $query = $this->model::query()->with($with);

        // Filter by family if provided
        if (isset($where['family_id'])) {
            $familyId = $where['family_id'];

            if (canViewAllFamilyExpenses($familyId)) {
                // Super admin can see all family expenses
                $query->where('family_id', $familyId);
            } else {
                // Regular members can only see their own expenses
                $query->where('family_id', $familyId)
                    ->where('user_id', auth()->id());
            }
        }

        $query->when(isset($where['from_date']), fn($q) => $q->whereDate('date', '>=', $where['from_date']))
            ->when(isset($where['to_date']), fn($q) => $q->whereDate('date', '<=', $where['to_date']))
            ->when(isset($where['type']), fn($q) => $q->where('type', $where['type']));

        return $query->paginate($paginate);
    }
    public function getUpcomingExpenses()
    {
        return $this->model::query()
            ->with('expenseCategory')
            ->whereDate('date', now()->subMinutes(60))
            ->where('notified_at', null)
            ->get();
    }
    public function getExpiredRecurring()
    {
        return $this->model::query()
            ->with('expenseCategory')
            ->whereDate('date', '<', now())
            ->where('type', ExpenseType::RECURRING)
            ->get();
    }
    public function homePage($familyId)
    {
        $categoriesQuery = DB::table('expenses')
            ->where('expenses.family_id', $familyId)
            ->selectRaw('COUNT(expenses.id) as total_expenses, expense_category_id, expense_categories.name')
            ->join('expense_categories', 'expense_categories.id', '=', 'expenses.expense_category_id')
            ->groupBy('expense_category_id');

        $totalQuery = DB::table('expenses')
            ->selectRaw('SUM(amount) as total')
            ->where('family_id', $familyId);

        // If not super admin, filter to only user's expenses
        if (!canViewAllFamilyExpenses($familyId)) {
            $categoriesQuery->where('expenses.user_id', auth()->id());
            $totalQuery->where('user_id', auth()->id());
        }

        $categories = $categoriesQuery->get();
        $total = $totalQuery->first();

        return compact('categories', 'total');
    }
}
