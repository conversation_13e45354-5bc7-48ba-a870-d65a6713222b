<?php

namespace App\Services;

use App\Enums\EventType;
use App\Models\Event;
use App\Services\Service;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class EventService extends Service
{

    public function model()
    {
        return Event::class;
    }
    public function getlatest(string $familyId, ?string $date = null, $groupBy = 'day')
    {
        return $this->model::query()
            ->with(['family', 'createdBy', 'occasion'])
            ->when($date, fn($query) => $query->when($groupBy == 'day', fn($query) => $query->whereDate('scheduled_at', $date), )
                ->when($groupBy == 'month', fn($query) => $query->whereMonth('scheduled_at', $date)))
            ->when(!$date, fn($query) => $query->whereBetween('created_at', [now()->subDays(7), now()->addDays(7)]))
            ->where('family_id', $familyId)
            ->orderBy('scheduled_at', 'asc')
            ->get();
    }
    public function getEvent(string $id, ?array $with = [], ?string $userId)
    {
        return $this->model::query()
            ->when($userId, fn($q) => $q->whereHas('family', fn($q) => $q->whereHas('users', fn($q) => $q->where('users.id', $userId))))
            ->with($with)
            ->findOrFail($id);
    }
    /**
     * get notifiable events
     */
    public function getNotifiableEvents()
    {
        return $this->model::query()
            ->whereDate('notify_time', '<=', now())
            ->whereNull('notified_at')
            ->get();
    }
    public function getExpiredRecurring()
    {
        return $this->model::query()
            ->where('type', EventType::RECURRING)
            ->whereDate('scheduled_at', '<', now())
            ->get();
    }
}
