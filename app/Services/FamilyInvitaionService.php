<?php

namespace App\Services;

use App\Models\Family;
use App\Models\FamilyInvitation;
use App\Notifications\InviteByEmailNotification;
use App\Services\Service;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Notification;

class FamilyInvitaionService extends Service
{

    public function model()
    {
        return FamilyInvitation::class;
    }
    public function generateInvitaionCode()
    {
        $str = str()->random(5);
        $invitations = $this->model::query()->where('code', 'like', $str . '%')->pluck('code');
        do {
            $str .= rand(0, 999);
        } while ($invitations->contains($str));
        return $str;
    }
    public function invite($emails = [], Family $family)
    {
        foreach ($emails as $email) {
            $code = $this->generateInvitaionCode();
            $this->create([
                'invited_by' => auth()->id(),
                'family_id' => Filament::getTenant()->id,
                'invited_email' => $email,
                'code' => $code
            ]);
            Notification::route('mail', $email)
                ->notify(new InviteByEmailNotification($email, $code, $family->name));
        }
    }
}
