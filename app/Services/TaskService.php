<?php

namespace App\Services;

use App\Models\Task;
use App\Services\Service;
use Illuminate\Support\Carbon;

class TaskService extends Service
{

    public function model()
    {
        return Task::class;
    }
    public function all(int|null $paginate = null, array $with = [], array $where = [], bool $getAsQuery = false)
    {
        $search = null;
        if (isset($where['search'])) {
            $search = $where['search'];
            unset($where['search']);
        }
        return parent::all($paginate, $with, $where, true)
            ->when($search, fn($q) => $q->where('title', 'like', $search . '%')
                ->orWhere('description', 'like', "$search%"))
            ->paginate($paginate);
    }
    public function create(array $data)
    {
        $assignees = [];
        if (isset($data['assignees']) && $data['assignees']) {
            $assignees = $data['assignees'];
            unset($data['assignees']);
        }
        $task = parent::create($data);
        $task->assignees()->sync($assignees);
        return $task;
    }
    public function update(array $data, string $id)
    {
        $assignees = $data['assignees'] ?? [];
        unset($data['assignees']);
        $task = parent::update($data, $id);
        $task->assignees()->sync($assignees);
        return $task;
    }
    public function getPrioritizedTasks(int $familyId, ?int $userId = null, int $paginate = 10, array $with = [], ?string $dueDate = null)
    {
        $query = $this->model::query()->where('family_id', $familyId);

        if ($userId) {
            $query->where('assigned_to', $userId);
        }

        if ($dueDate) {
            $query->whereDate('due_date', $dueDate);
        }

        if (!empty($with)) {
            $query->with($with);
        }

        $query->orderByRaw('
            CASE
                WHEN due_date IS NOT NULL AND due_date <= ? THEN 5
                WHEN due_date IS NOT NULL AND due_date <= ? THEN 3
                ELSE 1
            END DESC,
            updated_at DESC
        ', [Carbon::now()->addDays(1), Carbon::now()->addDays(3)]);

        return $query->paginate($paginate);
    }
    public function getApproachingTasks()
    {
        return $this->model::query()
            ->whereDate('due_date', now()->subMinutes(60))
            ->get();
    }
}
