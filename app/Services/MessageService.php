<?php

namespace App\Services;

use App\Enums\MessageType;
use App\Events\ChatEvent;
use App\Events\FamilyNotificationEvent;
use App\Events\MessageSent;
use App\Http\Resources\Chat\MessageResource;
use App\Http\Resources\User\UserResource;
use App\Models\Message;
use App\Models\User;
use App\Services\Service;
use Illuminate\Support\Facades\DB;

class MessageService extends Service
{

    public function model()
    {
        return Message::class;
    }
    public function create(array $data)
    {
        return DB::transaction(function () use ($data) {

            $message = parent::create([
                'chat_id' => $data['chat_id'],
                'content' => $data['content'],
                'type' => $data['type'],
                'user_id' => $data['user_id'],
                'uuid' => $data['uuid'] ?? null
            ]);
            if (isset($data['image'])) {
                $message->addMedia($data['image'])
                    ->toMediaCollection('image');
            } else if (isset($data['voice'])) {
                $message->addMedia($data['voice'])
                    ->toMediaCollection('voice');
            } else if (isset($data['file'])) {
                $message->addMedia($data['file'])
                    ->toMediaCollection('file');
            }
            if (isset($data['contact']) && $data['contact'] != null) {
                $message->contact()->create([
                    'name' => $data['contact']['name'],
                    'phones' => $data['contact_phones'] ?? []
                ]);
            }
            $message->load(['sender', 'image', 'file', 'voice', 'contact']);
            $message->chat->touch();
            broadcast(new MessageSent(MessageResource::make($message)));
            broadcast(new FamilyNotificationEvent($message, 'message', $message->chat->family_id));
            return $message;
        });
    }
    public function getChatMessages(string $chatId, string $userId)
    {
        return $this->model::query()
            ->where('chat_id', $chatId)
            ->whereHas('chat', fn($query) => $query->whereHas('members', fn($query) => $query->where('user_id', $userId)))
            ->with(['sender', 'image', 'voice', 'file', 'readBy', 'contact'])
            ->orderBy('created_at', 'desc')
            ->paginate(50);
    }
    public function readAll(string $userId, string $chatId)
    {
        $ids = $this->model::query()
            ->select('id')
            ->where('chat_id', $chatId)
            ->whereDoesntHave('reads', fn($query) => $query->where('message_reads.user_id', $userId))
            ->where('user_id', '!=', $userId)
            ->pluck('id');

        if ($ids->isEmpty())
            return $ids;

        $user = User::find($userId);
        $data = [];
        foreach ($ids as $item) {
            $data[] = ['message_id' => $item];
        }
        $user->messageReads()->createMany($data);
        return $ids;
    }
}
