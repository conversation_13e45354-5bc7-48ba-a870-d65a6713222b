<?php

namespace App\Notifications;

use App\Models\Event;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EventReminderNotification extends Notification
{
    use Queueable;
    public string $familyId;
    /**
     * Create a new notification instance.
     */
    public function __construct(public Event $event)
    {
        $this->familyId = $event->family_id;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'family'];
    }
    public function toFamily(object $notifiable)
    {
        return [
            'title' => 'Upcoming event',
            'body' => 'this is a reminder of your upcoming event ' . $this->event->name,
        ];
    }
    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Upcoming event')
            ->line('this is a reminder of your upcoming event ' . $this->event->name)
            ->line('event scheduled at: ' . $this->event->scheduled_at->toFormattedDateString());
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
