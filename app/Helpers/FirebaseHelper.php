<?php

namespace App\Helpers;

use Exception;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Messaging\CloudMessage;
use <PERSON>reait\Firebase\Messaging\Notification as FirebaseNotification;
use Kreait\Firebase\Messaging\MessageData;
use Kreait\Laravel\Firebase\Facades\Firebase;

class FirebaseHelper
{
    protected CloudMessage $cloudMessage;
    protected $firebase;
    public function __construct()
    {
        $this->cloudMessage = CloudMessage::new();
        $this->firebase = Firebase::messaging();
    }
    public function sendNotification($tokens, string $title, ?string $body = null, ?string $image = null, $data = [])
    {
        try {
            $notification = FirebaseNotification::create($title, $body ?? '', $image);
            $message = CloudMessage::new()
                ->withNotification($notification)
                ->withData($data)
            ;
            $this->firebase->sendMulticast($message, $tokens);
        } catch (Exception $e) {
            Log::info('sending notification error');
            Log::info($e->getMessage());
        }
    }

}
