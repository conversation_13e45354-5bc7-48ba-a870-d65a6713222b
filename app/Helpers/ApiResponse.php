<?php

namespace App\Helpers;


class ApiResponse
{

    public static function success($data = [])
    {
        return response()->json(['message' => 'success', 'data' => $data], 200);
    }
    public static function forbidden($message = 'forbidden')
    {
        return response()->json(['message' => $message], 403);
    }
    public static function unAuthenticated($message = 'un-authenticated')
    {

        return response()->json(['message' => $message], 401);
    }
    public static function badRequest($message = 'bad request')
    {
        return response()->json(['message' => $message], 400);
    }

    public static function notFound($message = 'Not found')
    {
        return response()->json(['message' => $message], 404);
    }
    public static function error($message = 'error', $code)
    {
        return response()->json(['message' => $message], $code);
    }
}
