<?php
namespace App\Helpers;

use Agence104\LiveKit\AccessToken;
use Agence104\LiveKit\AccessTokenOptions;
use Agence104\LiveKit\VideoGrant;

class Livekit
{
    public function generateToken(string $room)
    {
        // If this room doesn't exist, it'll be automatically created when the first
// participant joins.
        // The identifier to be used for participant.
        $participantName = auth()->user()->name;

        // Define the token options.
        $tokenOptions = (new AccessTokenOptions())
            ->setIdentity(auth()->id())
            ->setName($participantName);

        // Define the video grants.
        $videoGrant = (new VideoGrant())
            ->setRoomJoin()
            ->setRoomName($room);
        // Initialize and fetch the JWT Token.
        $token = (new AccessToken(config('livekit.api_key'), config('livekit.secret')))
            ->init($tokenOptions)
            ->setGrant($videoGrant)
            ->toJwt();
        return $token;
    }
}