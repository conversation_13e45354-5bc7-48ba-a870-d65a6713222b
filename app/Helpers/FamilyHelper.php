<?php

if (!function_exists('isFamilySuperAdmin')) {
    /**
     * Check if the authenticated user is a super admin of a specific family
     *
     * @param int $familyId
     * @return bool
     */
    function isFamilySuperAdmin($familyId)
    {
        if (!auth()->check()) {
            return false;
        }

        // Set permission team context
        setPermissionsTeamId($familyId);
        
        return auth()->user()->hasRole('super_admin');
    }
}

if (!function_exists('canViewAllFamilyExpenses')) {
    /**
     * Check if the authenticated user can view all family expenses
     *
     * @param int $familyId
     * @return bool
     */
    function canViewAllFamilyExpenses($familyId)
    {
        return isFamilySuperAdmin($familyId);
    }
}

if (!function_exists('canEditFamilyExpense')) {
    /**
     * Check if the authenticated user can edit a specific expense
     *
     * @param \App\Models\Expense $expense
     * @param int $familyId
     * @return bool
     */
    function canEditFamilyExpense($expense, $familyId)
    {
        // Super admin can edit any expense
        if (isFamilySuperAdmin($familyId)) {
            return true;
        }
        
        // Regular users can only edit their own expenses
        return $expense->user_id === auth()->id();
    }
}
