<?php

namespace App\Listeners;

use App\Events\CallEvent;
use App\Services\CallLogService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CallListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(CallEvent $event): void
    {
        if ($event->type == 'private') {

            $log = app(CallLogService::class)->create([
                'user_id' => auth()->id(),
                'chat_id' => $event->chat->id,
            ]);
            $users = $event->chat->members()->pluck('chat_members.user_id')->map(fn($member) => ['user_id' => $member]);
            $log->callUsers()->createMany($users->toArray());
            $log->callUsers()->where('user_id', auth()->id())
                ->update(['started_at' => now()]);
            return;
        }
        if ($event->type == 'accept') {
            $log = app(CallLogService::class)
                ->where('closed_at', null, '!=')
                ->where('chat_id', $event->chat->id)
                ->first();
            if (!$log->started_at) {
                $log->update([
                    'started_at' => now(),
                    'is_missed' => false
                ]);
            }
            $log->callUsers()->where('user_id', auth()->id())
                ->update(['started_at' => now()]);
            return;
        }
        if ($event->type == 'endCall') {
            $log = app(CallLogService::class)
                ->where('closed_at', null, '!=')
                ->where('chat_id', $event->chat->id)
                ->first();
            $log->callUsers()->where('user_id', auth()->id())
                ->update(['closed_at' => now()]);
            $check = $log->callUsers()->whereNull('closed_at')->exists();
            if (!$check) {
                $log->update(['closed_at' => now()]);
            }
            return;
        }

    }
}
