<?php

namespace App\Listeners;

use App\Enums\MessageType;
use App\Events\FamilyNotificationEvent;
use App\Facades\Firebase;
use App\Http\Resources\Chat\MessageResource;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class FamilyNotificationListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(FamilyNotificationEvent $event): void
    {
        $members = User::query()
            ->when(in_array($event->type, ['event']), fn($query) => $query->whereHas('families', fn($query) => $query->where('family_id', $event->familyId)))
            ->when($event->type == 'message', fn($query) => $query->whereHas(
                'chats.members',
                fn($query) => $query->where('chat_id', $event->data['chat_id'])

            )->where('id', '!=', $event->data->user_id))
            ->where('is_active', true)
            ->whereNotNull('fcm_token')
            ->pluck('fcm_token')
            ->toArray();
        if ($event->type == 'event') {
            $data = [
                'type' => 'event',
            ];
            $date = Carbon::createFromFormat('Y-m-d H:i:s', strtotime($event->data['scheduled_at']));
            Firebase::sendNotification($members, "Reminder: {$event->data['name']}", "Event time: {$date->toFormattedDateString()}", data: $data);
            return;

        }
        if ($event->type == 'message') {
            $this->handleMessageNotification($event, $members);
        }
    }
    protected function handleMessageNotification($event, $tokens)
    {
        $title = $event->data->sender->name;
        $body = $event->data->content;
        switch ($event->data->type) {
            case MessageType::IMAGE:
                $body = 'image';
                break;
            case MessageType::AUDIO:
                $body = 'voice message';
                break;
            case MessageType::VIDEO:
                $body = 'video';
                break;
            case MessageType::FILE:
                $body = 'Attachment';
                break;
        }
        $data = [
            'type' => 'message',
            'message' => json_encode(MessageResource::make($event->data))
        ];
        Firebase::sendNotification($tokens, $title, $body, $event->data->sender->getFirstMediaUrl('image'), $data);
    }
}
