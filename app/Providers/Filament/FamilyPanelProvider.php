<?php

namespace App\Providers\Filament;

use App\Filament\Family\Pages\EditFamily;
use App\Filament\Pages\RegisterFamily;
use App\Http\Middleware\HasFamilyMiddleware;
use <PERSON>zhanSalleh\FilamentShield\FilamentShieldPlugin;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class FamilyPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('family')
            ->path('family')
            ->login()
            ->spa()
            ->registration()
            ->brandName('Family Tracking')
            ->favicon(asset('favicon.ico'))
            ->darkMode(true)
            ->sidebarCollapsibleOnDesktop()
            ->tenant(\App\Models\Family::class, 'id', 'family')
            ->tenantRegistration(RegisterFamily::class)
            ->profile()
            ->tenantProfile(EditFamily::class)
            ->colors([
                'primary' => '#134982',
                'danger' => Color::Rose,
                'gray' => Color::Slate,
                'info' => Color::Sky,
                'success' => Color::Emerald,
                'warning' => Color::Orange,
            ])
            ->viteTheme('resources/css/filament/family/theme.css')
            ->plugins([
                FilamentShieldPlugin::make()
            ])
            ->discoverResources(in: app_path('Filament/Family/Resources'), for: 'App\\Filament\\Family\\Resources')
            ->discoverPages(in: app_path('Filament/Family/Pages'), for: 'App\\Filament\\Family\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Family/Widgets'), for: 'App\\Filament\\Family\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
            ])
            ->tenantMiddleware([
                HasFamilyMiddleware::class,
                \BezhanSalleh\FilamentShield\Middleware\SyncShieldTenant::class,
            ], isPersistent: true)
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
