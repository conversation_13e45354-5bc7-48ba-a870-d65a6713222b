<?php

namespace App\Providers;

use App\Channels\FamilyChannel;
use Illuminate\Broadcasting\BroadcastEvent;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind('livekit', fn() => new \App\Helpers\Livekit());
    }

    /**
     * Handle user connection event
     */
    protected function handleUserConnection($userId, $connectionId)
    {
        $user = \App\Models\User::find($userId);
        if (!$user)
            return;

        $status = $user->connectionStatus()->updateOrCreate(
            ['user_id' => $userId],
            [
                'is_online' => true,
                'connection_id' => $connectionId,
                'connected_at' => now(),
                'last_seen_at' => now(),
            ]
        );

        // Broadcast user online status to relevant channels
        broadcast(new \App\Events\UserStatusChanged($user, true));
    }

    /**
     * Handle user disconnection event
     */
    protected function handleUserDisconnection($userId, $connectionId)
    {
        $user = \App\Models\User::find($userId);
        if (!$user)
            return;

        $status = $user->connectionStatus;

        // Only mark as offline if this is the same connection ID
        if ($status && $status->connection_id === $connectionId) {
            $status->update([
                'is_online' => false,
                'disconnected_at' => now(),
                'last_seen_at' => now(),
            ]);

            // Broadcast user offline status to relevant channels
            broadcast(new \App\Events\UserStatusChanged($user, false));
        }
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Notification::extend('family', fn() => new FamilyChannel());
        Schema::defaultStringLength(191);
        Event::listen(BroadcastEvent::class, function (BroadcastEvent $event) {
            // Handle user presence events
            if (strpos($event->channelName, 'presence-user.') === 0) {
                $userId = str_replace('presence-user.', '', $event->channelName);

                if ($event->event == 'presence-joining') {
                    $this->handleUserConnection($userId, $event->connection);
                    Log::info('User connected: ' . $userId . ' with connection ' . $event->connection);
                }

                if ($event->event == 'presence-leaving') {
                    $this->handleUserDisconnection($userId, $event->connection);
                    Log::info('User disconnected: ' . $userId . ' with connection ' . $event->connection);
                }
            }

            // Handle family presence events
            if (strpos($event->channelName, 'presence-family.') === 0) {
                if ($event->event == 'presence-joining') {
                    Log::info('User joined family: ' . $event->connection);
                }

                if ($event->event == 'presence-leaving') {
                    Log::info('User left family: ' . $event->connection);
                }
            }
        });
    }
}
