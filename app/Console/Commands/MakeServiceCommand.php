<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;

class MakeServiceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:service {name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'create new service class';

    /**
     * Create a new command instance.
     *
     * @return void
     */

    public function __construct(protected Filesystem $file)
    {
        parent::__construct();
    }

    public function getStubPath(): string
    {
        return base_path('stubs/Service.stub');
    }

    public function getStubVariables(): array
    {
        $path = collect(explode('/', $this->argument('name')));
        $className = $path->pop();
        $namespace = "App\Services";

        if ($path->isNotEmpty()) {
            $namespace .= "\\{$path->join("\\")}";
        }

        return [
            'namespace' => $namespace,
            'class' => $className
        ];
    }

    /**
     * Get the stub path and the stub variables
     *
     * @return string
     *
     */
    public function getSourceFile(): string
    {
        return $this->getStubContents($this->getStubPath(), $this->getStubVariables());
    }

    /**
     * Replace the stub variables(key) with the desire value
     *
     * @param $stub
     * @param array $stubVariables
     * @return string
     */
    public function getStubContents($stub, array $stubVariables = []): string
    {
        $contents = file_get_contents($stub);

        foreach ($stubVariables as $search => $replace) {
            $contents = str_replace("{{ $search }}", $replace, $contents);
        }

        return $contents;
    }

    /**
     * Get the full path of generate class
     *
     * @return string
     */
    public function getSourceFilePath(): string
    {
        return base_path('app/Services') . '/' . $this->argument('name') . '.php';
    }

    /**
     * Build the directory for the class if necessary.
     *
     * @param  string  $path
     * @return void
     */
    protected function makeDirectory(string $path): void
    {
        if (!$this->file->isDirectory($path)) {
            $this->file->makeDirectory($path, 0777, true, true);
        }
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $path = $this->getSourceFilePath();

        if ($this->file->exists($path)) {
            $this->warn("File $path already exits");
            return self::FAILURE;
        }

        $this->makeDirectory(dirname($path));

        $contents = $this->getSourceFile();

        $this->file->put($path, $contents);
        $this->info("File $path created");
        return self::SUCCESS;
    }
}
