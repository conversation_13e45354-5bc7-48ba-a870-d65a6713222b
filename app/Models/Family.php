<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use ESolution\DBEncryption\Traits\EncryptedAttribute;

class Family extends Model
{
    /** @use HasFactory<\Database\Factories\FamilyFactory> */
    use HasFactory;


    protected $guarded = [];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    public function users()
    {
        return $this->belongsToMany(User::class, 'family_user')->withPivot('joined_at')
            ->withTimestamps();
    }

    public function invitations()
    {
        return $this->hasMany(FamilyInvitation::class);
    }

    public function chats()
    {
        return $this->hasMany(Chat::class);
    }

    public function groups()
    {
        return $this->hasMany(Group::class);
    }


    public function albums()
    {
        return $this->hasMany(Album::class);
    }

    public function storageOptimizations()
    {
        return $this->hasMany(StorageOptimization::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }


    public function events()
    {
        return $this->hasMany(Event::class);
    }

    public function shoppingLists()
    {
        return $this->hasMany(ShoppingList::class);
    }

    public function tasks()
    {
        return $this->hasMany(Task::class);
    }
    public function expenseCategories()
    {
        return $this->hasMany(ExpenseCategory::class);
    }
    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }
    public function roles()
    {
        return $this->hasMany(Role::class);
    }
    public function occasions()
    {
        return $this->hasMany(Occasion::class);
    }
    public function areas()
    {
        return $this->hasMany(Area::class);
    }
}
