<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CallLog extends Model
{
    protected $guarded = [];

    protected $casts = [
        'started_at' => 'datetime',
        'closed_at' => 'datetime',
        'is_missed' => 'boolean'
    ];
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function callUsers()
    {
        return $this->hasMany(CallUser::class);
    }
    public function chat()
    {
        return $this->belongsTo(Chat::class);
    }
}
