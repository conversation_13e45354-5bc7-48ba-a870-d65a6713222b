<?php

namespace App\Models;

use App\Enums\InvitationStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Notifications\Notification;

class FamilyInvitation extends Model
{
    use Notifiable;
    protected $guarded = [];

    protected $casts = [
        'status' => InvitationStatus::class
    ];

    public function family()
    {
        return $this->belongsTo(Family::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'invited_by');
    }
    public function routeNotificationForMail(Notification $notification)
    {
        return $this->invited_email;
    }

}
