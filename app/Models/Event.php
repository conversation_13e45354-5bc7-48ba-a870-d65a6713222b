<?php

namespace App\Models;

use App\Enums\EventType;
use App\Enums\RecurringPattern;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Event extends Model
{
    use HasFactory;
    protected $guarded = [];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'notified_at' => 'datetime',
        'notify_time' => 'datetime',
        'type' => EventType::class,
        'recurring_pattern' => RecurringPattern::class
    ];
    public function family()
    {
        return $this->belongsTo(Family::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    public function occasion()
    {
        return $this->belongsTo(Occasion::class);
    }
}
