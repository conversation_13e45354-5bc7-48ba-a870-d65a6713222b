<?php

namespace App\Models;

use App\Enums\ShoppingListType;
use Illuminate\Database\Eloquent\Model;

class ShoppingList extends Model
{
    protected $guarded = [];

    protected $casts = [
        'type' => ShoppingListType::class
    ];
    public function family()
    {
        return $this->belongsTo(Family::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function items()
    {
        return $this->hasMany(ShoppingListItem::class);
    }
}
