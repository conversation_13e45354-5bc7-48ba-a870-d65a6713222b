<?php

namespace App\Models;

use App\Enums\AlbumType;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Album extends Model implements HasMedia
{
    use InteractsWithMedia;
    protected $guarded = [];

    protected $casts = [
        'type' => AlbumType::class
    ];
    public function family()
    {
        return $this->belongsTo(Family::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function gallery()
    {
        return $this->media()->where('collection_name', 'gallery');
    }

    public function assignments()
    {
        return $this->hasMany(AlbumAssignment::class);
    }

    public function assignedUsers()
    {
        return $this->belongsToMany(User::class, 'album_assignments');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Check if a user can view this album
     */
    public function canUserView($userId)
    {
        // Creator can always view
        if ($this->user_id == $userId) {
            return true;
        }

        // Check if user is assigned to this album
        return $this->assignments()->where('user_id', $userId)->exists();
    }

    /**
     * Check if a user can edit this album
     */
    public function canUserEdit($userId)
    {
        // Only creator can edit
        return $this->user_id == $userId;
    }
}
