<?php

namespace App\Models;

use App\Observers\PollVoteObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

#[ObservedBy(PollVoteObserver::class)]
class PollVote extends Model
{
    /** @use HasFactory<\Database\Factories\PollVoteFactory> */
    use HasFactory;

    protected $guarded = [];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function pollItem()
    {
        return $this->belongsTo(PollItem::class);
    }
    public function poll()
    {
        return $this->belongsTo(Poll::class);
    }
}
