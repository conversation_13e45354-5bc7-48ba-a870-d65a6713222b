<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Poll extends Model
{
    /** @use HasFactory<\Database\Factories\PollFactory> */
    use HasFactory;

    protected $guarded = [];

    protected $casts = ['is_open' => 'boolean'];

    public function items()
    {
        return $this->hasMany(PollItem::class);
    }

    public function votes()
    {
        return $this->hasMany(PollVote::class);
    }

    public function assignments()
    {
        return $this->hasMany(PollAssignment::class);
    }

    public function assignedUsers()
    {
        return $this->belongsToMany(User::class, 'poll_assignments');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Check if a user can view this poll
     */
    public function canUserView($userId)
    {
        // Creator can always view
        if ($this->user_id == $userId) {
            return true;
        }

        // Check if user is assigned to this poll
        return $this->assignments()->where('user_id', $userId)->exists();
    }

    /**
     * Check if a user can vote on this poll
     */
    public function canUserVote($userId)
    {
        // Only assigned users can vote (creator cannot vote unless assigned)
        return $this->assignments()->where('user_id', $userId)->exists() || $this->user_id == auth()->id();
    }

}
