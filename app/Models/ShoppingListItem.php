<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ShoppingListItem extends Model
{
    protected $guarded = [];
    protected $casts = [
        'is_purchased' => 'boolean'
    ];
    public function shoppingList()
    {
        return $this->belongsTo(ShoppingList::class);
    }

    public function addedBy()
    {
        return $this->belongsTo(User::class, 'addeed_by');
    }
}
