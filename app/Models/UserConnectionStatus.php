<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserConnectionStatus extends Model
{
    protected $guarded = [];

    protected $casts = [
        'is_online' => 'boolean',
        'last_seen_at' => 'datetime',
        'connected_at' => 'datetime',
        'disconnected_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
