<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Observers\UserObserver;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasTenants;
use Filament\Panel;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use Laravel\Sanctum\HasApiTokens;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Permission\Traits\HasRoles;
use Laravolt\Avatar\Facade as Avatar;

#[ObservedBy(UserObserver::class)]
class User extends Authenticatable implements FilamentUser, HasTenants, HasMedia
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles, HasApiTokens, InteractsWithMedia;

    /**
     * The attributes that should be encrypted on save.
     *
     * @var array
     */
    // protected $encryptable = ['name'];

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $guarded = [];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];
    public function createDefaultAvatar()
    {
        $name = $this->name ?? 'User'; // Use user name or a default
        $avatar = Avatar::create($name)->toBase64();

        // Convert base64 to an image file
        $avatarData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $avatar));
        $directoryPath = storage_path("app/public/avatars");
        $filePath = "$directoryPath/{$this->id}.png";

        if (!file_exists($directoryPath)) {
            mkdir($directoryPath, 0777, true);
        }
        file_put_contents($filePath, $avatarData);

        // Add avatar to media library
        $this->addMedia($filePath)
            ->preservingOriginal()
            ->toMediaCollection('image');
    }
    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_admin' => 'boolean',
            'is_active' => 'boolean'
        ];
    }
    public function canAccessPanel(Panel $panel): bool
    {
        if ($panel->getId() == 'admin')
            return $this->is_admin;
        return true;
    }
    public function families()
    {
        return $this->belongsToMany(Family::class, 'family_user')->withPivot('joined_at')
            ->withTimestamps();
    }

    public function getTenants(Panel $panel): array|Collection
    {
        return $this->families;
    }
    public function canAccessTenant(Model $tenant): bool
    {
        return $tenant->users()->where('user_id', $this->id)->exists();
    }

    public function sentInvitations()
    {
        return $this->hasMany(FamilyInvitation::class, 'invited_by');
    }
    public function chats()
    {
        return $this->belongsToMany(Chat::class, 'chat_members')->withPivot(['is_archived']);
    }

    public function createdGroups()
    {
        return $this->hasMany(Group::class, 'created_by');
    }

    public function sentGroupMessages()
    {
        return $this->hasMany(GroupMessage::class, 'sender_id');
    }

    public function groupMemberships()
    {
        return $this->hasMany(GroupMember::class);
    }

    public function groups()
    {
        return $this->belongsToMany(Group::class, 'group_members')
            ->withPivot('role')
            ->withTimestamps();
    }


    public function albums()
    {
        return $this->hasMany(Album::class, );
    }


    public function events()
    {
        return $this->hasMany(Event::class, 'created_by');
    }

    public function shoppingLists()
    {
        return $this->hasMany(ShoppingList::class, 'created_by');
    }

    public function tasksAssigned()
    {
        return $this->hasMany(Task::class, 'assigned_to');
    }

    public function tasksCreated()
    {
        return $this->hasMany(Task::class, 'created_by');
    }
    public function verificationCode()
    {
        return $this->hasOne(VerificationCode::class);
    }
    public function messageReads()
    {
        return $this->hasMany(MessageRead::class);
    }

    public function connectionStatus()
    {
        return $this->hasOne(UserConnectionStatus::class);
    }

    public function isOnline()
    {
        return $this->connectionStatus?->is_online ?? false;
    }

    /**
     * Check if user is super admin of a specific family
     */
    public function isFamilySuperAdmin($familyId = null)
    {
        if ($familyId) {
            // Set the team context for permission checking
            setPermissionsTeamId($familyId);
        }

        return $this->hasRole('super_admin');
    }
}
