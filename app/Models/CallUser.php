<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CallUser extends Model
{
    protected $guarded = [];

    protected $casts = [
        'started_at' => 'datetime',
        'closed_at' => 'datetime'
    ];
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function log()
    {
        return $this->belongsTo(CallLog::class);
    }
}
