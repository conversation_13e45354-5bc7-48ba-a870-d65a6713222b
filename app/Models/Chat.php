<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use ESolution\DBEncryption\Traits\EncryptedAttribute;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Chat extends Model implements HasMedia
{
    use EncryptedAttribute, InteractsWithMedia;

    protected $encryptable = ['content'];

    protected $guarded = [];

    protected $casts = ['is_group' => 'boolean'];

    public function family()
    {
        return $this->belongsTo(Family::class);
    }

    public function members()
    {
        return $this->belongsToMany(User::class, 'chat_members')
            ->withPivot(['is_archived'])
            ->withTimestamps();
    }
    public function chatMembers()
    {
        return $this->hasMany(ChatMember::class);
    }
    public function messages()
    {
        return $this->hasMany(Message::class);
    }
    public function lastMessage()
    {
        return $this->hasOne(Message::class)->latest();
    }
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    public function image()
    {
        return $this->media()->where('collection_name', 'image')->one();
    }
    public function calls()
    {
        return $this->hasMany(CallLog::class);
    }
}
