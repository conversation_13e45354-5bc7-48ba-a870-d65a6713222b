<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class SubscriptionPlan extends Model
{
    use HasFactory, HasTranslations;

    protected $guarded = [];

    protected $casts = [
        'is_default' => 'boolean',
    ];
    public $translatable = ['name'];

    public function families()
    {
        return $this->hasMany(Family::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }
}
