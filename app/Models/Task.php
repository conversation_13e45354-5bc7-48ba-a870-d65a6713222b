<?php

namespace App\Models;

use App\Enums\RecurringPattern;
use App\Enums\TaskPriority;
use App\Enums\TaskStatus;
use App\Enums\TaskType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Task extends Model
{
    use HasFactory;
    protected $guarded = [];

    protected $casts = [
        'priority' => TaskPriority::class,
        'status' => TaskStatus::class,
        'type' => TaskType::class,
        'recurring_pattern' => RecurringPattern::class
    ];
    public function family()
    {
        return $this->belongsTo(Family::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }


    public function subTasks()
    {
        return $this->hasMany(SubTask::class);
    }
    public function assignees()
    {
        return $this->belongsToMany(User::class, 'task_assignees');
    }
}
