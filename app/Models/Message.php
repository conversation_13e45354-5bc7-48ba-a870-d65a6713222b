<?php

namespace App\Models;

use App\Enums\MessageType;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Message extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    protected $casts = [
        'type' => MessageType::class,
        'chat_id' => 'integer'
    ];

    public function chat()
    {
        return $this->belongsTo(Chat::class);
    }
    public function sender()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function image()
    {
        return $this->media()->where('collection_name', 'image')->one();
    }
    public function file()
    {
        return $this->media()->where('collection_name', 'file')->one();
    }
    public function voice()
    {
        return $this->media()->where('collection_name', 'voice')->one();
    }
    public function reads()
    {
        return $this->hasMany(MessageRead::class);
    }
    public function readBy()
    {
        return $this->belongsToMany(User::class, 'message_reads');
    }

    public function contact()
    {
        return $this->hasOne(SharedContact::class);
    }
}
