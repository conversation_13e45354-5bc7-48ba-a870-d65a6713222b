<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Occasion extends Model implements HasMedia
{
    /** @use HasFactory<\Database\Factories\OccasionFactory> */
    use HasFactory, InteractsWithMedia;

    protected $guarded = [];

    protected $casts = ['is_active' => 'boolean'];
    public function family()
    {
        return $this->belongsTo(Family::class);
    }
}
