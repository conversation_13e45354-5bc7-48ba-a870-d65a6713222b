<?php

namespace App\Channels;

use App\Facades\Firebase;
use App\Models\Family;
use App\Models\User;
use Log;

class FamilyChannel
{
    public function send($notifiable, $notification)
    {
        if (!$notification->familyId)
            return;
        $tokens = Family::find($notification->familyId)->users()->pluck('fcm_token');
        Firebase::sendNotification($tokens->toArray(), $notification->title, $notification->body);
    }
}
