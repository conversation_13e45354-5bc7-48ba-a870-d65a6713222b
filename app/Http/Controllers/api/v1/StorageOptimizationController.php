<?php

namespace App\Http\Controllers\api\v1;

use App\Models\Family;
use App\Helpers\ApiResponse;
use Illuminate\Http\Request;
use App\Jobs\AnalyzeStorageJob;
use App\Models\StorageOptimization;
use App\Http\Controllers\Controller;
use App\Jobs\ProcessFamilyStorageJob;
use App\Http\Resources\StorageOptimizationResource;

class StorageOptimizationController extends Controller
{
    public function analyze(Family $family)
    {
        // 1. Check if analysis is in progress
        if ($family->is_analysis_in_progress) {
            return response()->json([
                'message' => 'Analysis is already in progress. Please wait for the results.',
                'status' => 'in_progress',
            ]);
        }

        // 2. Start analysis if not already in progress
        $family->update(['is_analysis_in_progress' => true]);
        ProcessFamilyStorageJob::dispatch($family);

        return response()->json([
            'message' => 'Analysis has been started. Please check back for results.',
            'status' => 'started',
        ]);
    }

    public function getResults(Family $family)
    {
        $optimization = StorageOptimization::where('family_id', $family->id)->first();
        if (!$optimization) {
            return ApiResponse::notFound();
        }
        return new StorageOptimizationResource($optimization);
    }
}
