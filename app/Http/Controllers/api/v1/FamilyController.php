<?php

namespace App\Http\Controllers\api\v1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Family\InviteRequest;
use App\Http\Resources\User\UserResource;
use App\Notifications\InviteByEmailNotification;
use App\Rules\FamilyUserRule;
use App\Services\FamilyInvitaionService;
use App\Services\FamilyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;

class FamilyController extends Controller
{
    public function __construct(protected FamilyService $familyService) {}

    public function invite(InviteRequest $request, FamilyInvitaionService $familyInvitaionService)
    {
        $family = $this->familyService->show($request->input('family_id'));
        $familyInvitaionService->invite($request->input('emails'), $family);
        return ApiResponse::success();
    }
    /**
     * get family members
     * @param \Illuminate\Http\Request $request
     * @return mixed|\Illuminate\Http\JsonResponse
     */
    public function members(Request $request)
    {
        $members = $this->familyService->getSharedMembers(auth()->id());
        return ApiResponse::success(UserResource::collection($members));
    }
}
