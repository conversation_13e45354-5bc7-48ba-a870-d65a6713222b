<?php

namespace App\Http\Controllers\api\v1;

use App\Enums\TaskStatus;
use App\Helpers\ApiResponse;
use App\Models\Task;
use App\Rules\FamilyUserRule;
use Illuminate\Http\Request;
use App\Services\TaskService;
use App\Http\Controllers\Controller;
use App\Http\Resources\TaskResource;
use App\Http\Requests\Task\CreateTaskRequest;
use App\Http\Requests\Task\UpdateTaskRequest;
use App\Http\Resources\Event\EventResource;
use App\Services\EventService;
use Illuminate\Support\Facades\DB;

class TaskController extends Controller
{
    public function __construct(protected TaskService $taskService)
    {
    }
    public function index(Request $request, EventService $eventService)
    {
        $request->validate([
            'family_id' => ['required', new FamilyUserRule],
            'status' => 'nullable',
            'search' => 'nullable'
        ]);

        $tasks = $this->taskService
            ->orderBy('created_at', 'desc')
            ->all(
                10,
                ['createdBy', 'subTasks' => fn($query) => $query->orderBy('sort'), 'assignees'],
                $request->only(['family_id', 'status', 'search'])
            );
        return ApiResponse::success(TaskResource::collection($tasks));
    }
    public function show($id, Request $request)
    {
        $request->validate(['family_id' => ['required', new FamilyUserRule]]);
        $task = $this->taskService->show($id, ['createdBy', 'assignees', 'subTasks'], ['family_id' => $request->input('family_id')]);
        return ApiResponse::success($task);
    }
    public function store(CreateTaskRequest $request)
    {
        $validatedData = $request->validated();

        $task = $this->taskService->create($validatedData);
        $task->load('createdBy');
        return ApiResponse::success(TaskResource::make($task));
    }

    public function update(UpdateTaskRequest $request, $id)
    {
        $task = $this->taskService->update($request->validated(), $id);
        return ApiResponse::success(TaskResource::make($task));
    }


    public function destroy($id)
    {
        $this->taskService->delete([$id]);
        return ApiResponse::success();
    }
}
