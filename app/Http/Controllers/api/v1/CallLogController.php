<?php

namespace App\Http\Controllers\api\v1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\Call\CallLogResource;
use App\Services\CallLogService;
use Illuminate\Http\Request;

class CallLogController extends Controller
{
    public function __construct(protected CallLogService $callLogService)
    {
    }

    public function index()
    {
        $logs = $this->callLogService->userLog(auth()->id());
        return ApiResponse::success(CallLogResource::collection($logs));
    }
}
