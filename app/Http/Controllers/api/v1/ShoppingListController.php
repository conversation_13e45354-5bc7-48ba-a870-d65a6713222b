<?php

namespace App\Http\Controllers\api\v1;

use App\Enums\ShoppingListType;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\ShoppingList\CreateShoppingListRequest;
use App\Http\Resources\Family\ShoppingListResource;
use App\Models\ShoppingList;
use App\Models\ShoppingListItem;
use App\Rules\FamilyUserRule;
use App\Services\ShoppingListService;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ShoppingListController extends Controller
{
    public function __construct(protected ShoppingListService $service)
    {
    }

    public function index(Request $request)
    {
        $request->validate([
            'family_id' => ['required', new FamilyUserRule],
            'group_by' => ['nullable', 'in:week,day'],
            'type' => ['nullable', Rule::in(array_column(ShoppingListType::cases(), 'value'))]
        ]);
        $result = $this->service->orderBy('created_at', 'desc')->all(paginate: 20, with: ['createdBy', 'items'], where: $request->only('family_id', 'type'));
        return ApiResponse::success(ShoppingListResource::collection($result));
    }
    /**
     * add meal
     * @param \App\Http\Requests\ShoppingList\CreateShoppingListRequest $request
     * @return mixed|\Illuminate\Http\JsonResponse
     */
    public function store(CreateShoppingListRequest $request)
    {
        $result = $this->service->create($request->validated());
        $result->load(['createdBy', 'items']);
        return ApiResponse::success(ShoppingListResource::make($result));
    }
    /**
     * delete meal
     * @param mixed $id
     * @return mixed|\Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $this->service->delete([$id]);
        return ApiResponse::success();
    }
    public function checkItem($id)
    {
        $item = ShoppingListItem::whereHas('shoppingList', fn($query) =>
            $query->whereHas('family', fn($query) => $query->whereHas(
                'users',
                fn($query) => $query->where('user_id', auth()->id())
            )))
            ->findOrFail($id);
        $item->is_purchased = !$item->is_purchased;
        $item->save();
        return ApiResponse::success();
    }
}
