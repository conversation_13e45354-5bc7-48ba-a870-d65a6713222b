<?php

namespace App\Http\Controllers\api\v1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\NotificationResource;
use App\Services\NotificationService;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    public function __invoke()
    {
        $result = auth()->user()->notifications()->paginate();
        return ApiResponse::success(NotificationResource::collection($result));
    }
}
