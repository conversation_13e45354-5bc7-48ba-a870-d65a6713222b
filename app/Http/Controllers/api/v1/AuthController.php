<?php

namespace App\Http\Controllers\api\v1;

use App\Enums\InvitationStatus;
use App\Helpers\ApiResponse;
use App\Http\Requests\Invite\InviteByEmailRequest;
use App\Http\Requests\Invite\InviteMultipleEmailsRequest;
use App\Notifications\InviteByEmailNotification;
use App\Services\FamilyInvitaionService;
use App\Services\UserService;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\ForgetPasswordRequest;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Family\CreateFamilyRequest;
use App\Http\Resources\Family\FamilyResource;
use App\Http\Resources\User\UserResource;
use App\Models\FamilyInvitation;
use App\Models\User;
use App\Services\FamilyService;
use App\Services\VerificationCodeService;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AuthController extends Controller
{
    public function __construct(
        protected UserService $userService,
        protected FamilyService $familyService
    ) {
    }
    public function login(LoginRequest $request)
    {
        $credentials = $request->validated();

        if (auth()->attempt($request->only(['email', 'password']))) {
            $user = auth()->user();
            $user->load(['families', 'roles']);
            $token = auth()->user()->createToken('api_token')->plainTextToken;
            if ($request->string('fcm_token')) {
                $user->fcm_token = $request->string('fcm_token');
                $user->save();
            }
            return ApiResponse::success(['user' => UserResource::make($user), 'token' => $token]);
        }
        return ApiResponse::unAuthenticated();
    }

    public function register(RegisterRequest $request)
    {
        $data = $request->validated();
        $familyId = null;
        if (isset($data['invite_code'])) {
            $code = $data['invite_code'];
            $invite = FamilyInvitation::where('code', $code)->first();
            if ($invite->status != InvitationStatus::PENDING) {
                return ApiResponse::badRequest(__('invalid invitation code'));
            }
            $invite->update([
                'status' => InvitationStatus::ACCEPTED,
            ]);
            $familyId = $invite->family_id;
        }
        unset($data['invite_code']);
        $user = $this->userService->create($data);
        if ($familyId) {
            $user->families()->attach($familyId);
        }
        $user->load(['families', 'roles']);
        auth()->login($user);
        $token = auth()->user()->createToken('api_token')->plainTextToken;
        return ApiResponse::success(['user' => UserResource::make($user), 'token' => $token]);
    }
    public function registerFamily(CreateFamilyRequest $request)
    {

        $user = auth()->user();
        $family = $this->familyService->create($request->validated());
        setPermissionsTeamId($family->id);
        $superAdmin = Role::firstOrCreate([
            'name' => 'super_admin',
            'guard_name' => 'web',
            // 'family_id' => $family->id
        ]);
        Role::firstOrCreate([
            'name' => 'family_member',
            'guard_name' => 'web',
            // 'family_id' => $family->id
        ]);
        $permissions = Permission::all();
        $superAdmin->syncPermissions($permissions);
        $user->assignRole($superAdmin);
        $family->users()->sync([
            [
                'user_id' => $user->id,
                'joined_at' => now()
            ]
        ]);
        $user->load(['families', 'roles']);
        return ApiResponse::success(['user' => UserResource::make($user)]);
    }
    /**
     * invite members by email
     * @param \App\Http\Requests\Invite\InviteByEmailRequest $request
     * @param \App\Services\FamilyInvitaionService $familyInvitaionService
     * @param \App\Services\FamilyService $familyService
     * @return mixed|\Illuminate\Http\JsonResponse
     */
    public function inviteByEmail(InviteByEmailRequest $request, FamilyInvitaionService $familyInvitaionService, FamilyService $familyService)
    {
        $email = $request->input('email');
        $check = $this->userService->first([], ['email' => $email]);
        if ($check) {
            return ApiResponse::badRequest(__("User already registered"));
        }
        $family = $familyService->show($request->input('family_id'));
        $code = $familyInvitaionService->generateInvitaionCode();
        $data = [
            'invited_email' => $email,
            'code' => $code,
            'invited_by' => auth()->id(),
            'family_id' => $family->id,
        ];
        $invite = $familyInvitaionService->create($data);

        $invite->notify(new InviteByEmailNotification($email, $code, $family->name));
        return ApiResponse::success();
    }

    /**
     * invite multiple members by email
     * @param \App\Http\Requests\Invite\InviteMultipleEmailsRequest $request
     * @param \App\Services\FamilyInvitaionService $familyInvitaionService
     * @param \App\Services\FamilyService $familyService
     * @return mixed|\Illuminate\Http\JsonResponse
     */
    public function inviteMultipleByEmail(InviteMultipleEmailsRequest $request, FamilyInvitaionService $familyInvitaionService, FamilyService $familyService)
    {
        $emails = $request->input('emails');
        $family = $familyService->show($request->input('family_id'));

        $check = User::whereIn('email', $emails)->pluck('email');

        foreach ($emails as $email) {

            if ($check->contains($email)) {

                continue;
            }

            $code = $familyInvitaionService->generateInvitaionCode();
            $data = [
                'invited_email' => $email,
                'code' => $code,
                'invited_by' => auth()->id(),
                'family_id' => $family->id,
            ];

            $invite = $familyInvitaionService->create($data);
            $invite->notify(new InviteByEmailNotification($email, $code, $family->name));
            $successfulInvites[] = $email;
        }

        return ApiResponse::success();
    }
    /**
     * send password reset code to user
     * @param \Illuminate\Http\Request $request
     * @param \App\Services\VerificationCodeService $verificationCodeService
     * @return mixed|\Illuminate\Http\JsonResponse
     */
    public function forgetPassword(Request $request, VerificationCodeService $verificationCodeService)
    {
        $request->validate(['email' => ['required', 'email', 'exists:users,email']]);
        $user = $this->userService->where('email', $request->input('email'))->first();
        $verificationCodeService->generate($user);
        return ApiResponse::success();
    }
    /**
     * check if the code is valid
     * @param \Illuminate\Http\Request $request
     * @return mixed|\Illuminate\Http\JsonResponse
     */
    public function checkCode(Request $request)
    {
        $request->validate([
            'code' => [
                'required' => ['required', 'exists:verification_codes,code'],
                'email' => ['required', 'email', 'exists:users,email']
            ]
        ]);
        $user = $this->userService->where('email', $request->input('email'))->first();
        $code = $user->verificationCode()->where('code', $request->input('code'))
            ->where('user_id', $user->id)
            ->exists();
        if (!$code) {
            return ApiResponse::error(__('invalid code'), 422);
        }
        return ApiResponse::success();
    }
    /**
     * reset password with reset code
     * @param \App\Http\Requests\Auth\ForgetPasswordRequest $request
     * @return mixed|\Illuminate\Http\JsonResponse
     */
    public function resetPassword(ForgetPasswordRequest $request)
    {
        $data = $request->validated();
        $user = $this->userService->where('email', $data['email'])->first();
        $check = $user->verificationCode()->where('code', $data['code'])->exists();
        if (!$check) {
            return ApiResponse::error(__('invalid code'), 422);
        }
        $user->update([
            'password' => $data['password']
        ]);
        $user->verificationCode()->delete();
        return ApiResponse::success();
    }
}
