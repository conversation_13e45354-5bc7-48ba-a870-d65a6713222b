<?php

namespace App\Http\Controllers\api\v1;

use App\Events\DisableLocationStreamEvent;
use App\Events\UpdateLocationEvent;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Location\UpdateLocationRequest;
use App\Rules\FamilyUserRule;
use App\Services\UserService;
use Illuminate\Http\Request;

class LocationController extends Controller
{
    public function update(UpdateLocationRequest $request, UserService $userService)
    {
        $user = $userService->update($request->only(['lat', 'lng']), auth()->id());
        broadcast(new UpdateLocationEvent(
            $user,
            $request->input('family_id'),
            $request->input('metadata')
        ));
        return ApiResponse::success();
    }
    public function stopStreaming(Request $request)
    {
        $request->validate(['family_id' => ['required', new FamilyUserRule()]]);
        broadcast(new DisableLocationStreamEvent(auth()->user(), $request->input('family_id')));
        return ApiResponse::success();
    }
}
