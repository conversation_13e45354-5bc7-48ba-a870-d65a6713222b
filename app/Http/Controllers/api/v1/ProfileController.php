<?php

namespace App\Http\Controllers\api\v1;

use App\Helpers\ApiResponse;
use App\Services\FileUploadService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use App\Http\Resources\ProfileResource;
use App\Http\Requests\Profile\UploadImageRequest;
use App\Http\Requests\Profile\UpdateProfileRequest;
use App\Http\Resources\User\UserResource;

class ProfileController extends Controller
{

    public function __construct(
        protected FileUploadService $fileUploadService,
    ) {
    }

    public function show()
    {
        return new ProfileResource(auth()->user());
    }

    public function update(UpdateProfileRequest $request)
    {
        $user = auth()->user();

        $data = $request->only(['name', 'bio', 'time_zone']);

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->input('password'));
        }

        $user->update($data);
        $user->load(['families', 'roles']);
        return ApiResponse::success(UserResource::make($user));
    }

    public function updateProfilePicture(UploadImageRequest $request)
    {

        $user = auth()->user();
        $user->clearMediaCollection('image');
        $user->addMedia($request->file('image'))->toMediaCollection('image');
        $user->load(['families', 'roles']);
        return ApiResponse::success(UserResource::make($user));
    }
}
