<?php

namespace App\Http\Controllers\api\v1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserConnectionStatus;
use Illuminate\Http\Request;

class UserStatusController extends Controller
{
    /**
     * Get the status of a specific user
     */
    public function show($userId)
    {
        $user = User::with('connectionStatus')->find($userId);

        if (!$user) {
            return ApiResponse::notFound('User not found');
        }

        return ApiResponse::success([
            'user_id' => $user->id,
            'name' => $user->name,
            'is_online' => $user->isOnline(),
            'last_seen_at' => $user->connectionStatus?->last_seen_at,
        ]);
    }

    /**
     * Get the status of multiple users
     */
    public function batchStatus(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'required|exists:users,id'
        ]);

        $userIds = $request->input('user_ids');
        $users = User::with('connectionStatus')->whereIn('id', $userIds)->get();

        $statuses = $users->map(function ($user) {
            return [
                'user_id' => $user->id,
                'name' => $user->name,
                'is_online' => $user->isOnline(),
                'last_seen_at' => $user->connectionStatus?->last_seen_at,
            ];
        });

        return ApiResponse::success($statuses);
    }

    /**
     * Get the status of all family members
     */
    public function familyMembers(Request $request)
    {
        $request->validate([
            'family_id' => 'required|exists:families,id'
        ]);

        $familyId = $request->input('family_id');

        // Check if user belongs to this family
        $userBelongsToFamily = auth()->user()->families()->where('id', $familyId)->exists();
        if (!$userBelongsToFamily) {
            return ApiResponse::forbidden('You do not have access to this family');
        }

        $users = User::with('connectionStatus')
            ->whereHas('families', function ($query) use ($familyId) {
                $query->where('id', $familyId);
            })
            ->get();

        $statuses = $users->map(function ($user) {
            return [
                'user_id' => $user->id,
                'name' => $user->name,
                'is_online' => $user->isOnline(),
                'last_seen_at' => $user->connectionStatus?->last_seen_at,
            ];
        });

        return ApiResponse::success($statuses);
    }
}
