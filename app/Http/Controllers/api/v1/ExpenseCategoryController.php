<?php

namespace App\Http\Controllers\api\v1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Rules\FamilyUserRule;
use App\Services\ExpenseCategoryService;
use Illuminate\Http\Request;

class ExpenseCategoryController extends Controller
{
    public function __construct(protected ExpenseCategoryService $expenseCategoryService) {}
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $request->validate(['family_id' => ['required', new FamilyUserRule]]);
        $result = $this->expenseCategoryService->all(where: ['family_id' => $request->input('family_id'), 'is_active' => true]);
        return ApiResponse::success($result);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
