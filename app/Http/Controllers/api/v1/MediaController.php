<?php

namespace App\Http\Controllers\api\v1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Media\UploadMediaRequest;
use App\Http\Resources\MediaResource;
use App\Models\Album;
use App\Rules\FamilyUserRule;
use App\Services\MediaService;
use Illuminate\Http\Request;

class MediaController extends Controller
{
    public function __construct(protected MediaService $mediaService)
    {
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $request->validate(['family_id' => ['required', new FamilyUserRule]]);
        $media = $this->mediaService->all(paginate: 30, where: $request->only('family_id'));
        return ApiResponse::success(MediaResource::collection($media));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(UploadMediaRequest $request)
    {
        $albumId = $request->input('album_id');
        $album = Album::find($albumId);

        if (!$album) {
            return ApiResponse::notFound('Album not found');
        }

        // Check if user can view this album (assigned users can upload media)
        if (!$album->canUserView(auth()->id())) {
            return ApiResponse::forbidden('You do not have permission to upload media to this album');
        }

        $this->mediaService->create($request->validated());
        return ApiResponse::success();
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request)
    {
        $request->validate([
            'media' => ['required', 'array'],
            'media.*' => ['required', 'exists:media,id']
        ]);
        $this->mediaService->delete($request->array('media'));
        return ApiResponse::success();
    }
}
