<?php

namespace App\Http\Controllers\api\v1;

use App\Enums\EventType;
use App\Enums\TaskStatus;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\Event\EventResource;
use App\Http\Resources\TaskResource;
use App\Models\Event;
use App\Models\Task;
use App\Rules\FamilyUserRule;
use Illuminate\Http\Request;

class AchievementController extends Controller
{
    public function __invoke(Request $request)
    {
        $request->validate(['family_id' => 'required', new FamilyUserRule]);

        $tasks = Task::query()->with(['assignees', 'subTasks'])
            ->where('status', TaskStatus::DONE)
            ->where('family_id', $request->integer('family_id'))
            ->latest()
            ->limit(5)
            ->get();
        $events = Event::query()
            ->with(['createdBy', 'occasion'])
            ->whereDate('scheduled_at', '<', now())
            ->where('type', EventType::ONE_TIME)
            ->where('family_id', $request->integer('family_id'))
            ->latest()
            ->limit(5)
            ->get();
        $byOccasion = Event::query()
            ->where('family_id', $request->integer('family_id'))
            ->with(['createdBy', 'occasion'])
            ->whereDate('scheduled_at', '<', now())
            ->where('type', EventType::ONE_TIME)
            ->whereNotNull('occasion_id')
            ->latest()
            ->limit(5)
            ->groupBy('occasion_id')
            ->get();
        return ApiResponse::success([
            'tasks' => TaskResource::collection($tasks),
            'events' => EventResource::collection($events),
            'by_occasion' => EventResource::collection($byOccasion),
        ]);
    }
}
