<?php

namespace App\Http\Controllers\api\v1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Chats\CreateChatRequest;
use App\Http\Resources\Chat\ChatResource;
use App\Services\ChatService;
use App\Services\FamilyService;
use Illuminate\Http\Request;

class ChatController extends Controller
{
    public function __construct(protected ChatService $chatService)
    {
    }
    public function index(Request $request)
    {
        $request->validate([
            'is_archived' => ['nullable', 'boolean'],
            'search' => ['nullable', 'string', 'max:50']
        ]);
        $chats = $this->chatService->getUserChats(auth()->id(), archived: $request->boolean('is_archived') ?? false);

        return ApiResponse::success(ChatResource::collection($chats));
    }
    public function store(CreateChatRequest $request, FamilyService $familyService)
    {
        $familyId = $request->input('family_id');
        $membersArray = $request->input('members');
        $membersArray[] = auth()->id();
        $members = $familyService->getFamilyMembers($familyId, getAsQuery: true)
            ->whereIn('id', $membersArray)
            ->get();
        $chat = $this->chatService->create([
            'name' => $request->input('chat_name') ?? implode(',', $members->pluck('name')->toArray()) . ' chat',
            'is_group' => $members->count() > 2,
            'family_id' => $familyId
        ]);
        $chat->members()->sync($members->pluck('id')->toArray());
        $chat->load(['members', 'createdBy', 'image', 'lastMessage']);
        return ApiResponse::success(ChatResource::make($chat));
    }
    public function update(Request $request)
    {
    }

    public function show($id)
    {
        $chat = $this->chatService->getChatDetails($id, auth()->id());
        return ApiResponse::success($chat);
    }

    public function destroy($id)
    {
        $chat = $this->chatService->show($id);
        if ($chat) {
            $chat->chatMembers()->where('user_id', auth()->id())->delete();
        }
        return ApiResponse::success();
    }
    /**
     * archive or un-archive a chat
     * @param mixed $chatId
     * @return mixed|\Illuminate\Http\JsonResponse
     */
    public function archive($chatId)
    {
        $result = $this->chatService->archive(auth()->user(), $chatId);
        return ApiResponse::success($result);
    }
}
