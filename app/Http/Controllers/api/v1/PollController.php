<?php

namespace App\Http\Controllers\api\v1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Poll\CreatePollRequest;
use App\Http\Requests\Poll\UpdatePollRequest;
use App\Http\Requests\Poll\VoteRequest;
use App\Http\Resources\Poll\PollResource;
use App\Rules\FamilyUserRule;
use App\Services\PollService;
use Illuminate\Http\Request;

class PollController extends Controller
{
    public function __construct(protected PollService $pollService)
    {
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $request->validate(['family_id' => ['required', new FamilyUserRule]]);
        $familyId = $request->input('family_id');
        $userId = auth()->id();

        // Get polls where user is either creator or assigned
        $polls = $this->pollService->model()::with(['items', 'votes', 'assignments', 'assignedUsers'])
            ->where('family_id', $familyId)
            ->where(function ($query) use ($userId) {
                $query->where('user_id', $userId) // Creator
                    ->orWhereHas('assignments', function ($q) use ($userId) {
                        $q->where('user_id', $userId); // Assigned user
                    });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return ApiResponse::success(PollResource::collection($polls));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreatePollRequest $request)
    {
        $result = $this->pollService->create($request->validated());
        return ApiResponse::success(PollResource::make($result));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $poll = $this->pollService->model()::with(['items', 'votes', 'assignments', 'assignedUsers'])->find($id);

        if (!$poll) {
            return ApiResponse::notFound('Poll not found');
        }

        // Check if user can view this poll
        if (!$poll->canUserView(auth()->id())) {
            return ApiResponse::forbidden('You do not have permission to view this poll');
        }

        return ApiResponse::success(PollResource::make($poll));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePollRequest $request, string $id)
    {
        $poll = $this->pollService->model()::find($id);

        if (!$poll) {
            return ApiResponse::notFound('Poll not found');
        }

        // Only creator can update the poll
        if ($poll->user_id !== auth()->id()) {
            return ApiResponse::forbidden('Only the poll creator can update this poll');
        }

        $result = $this->pollService->update($request->validated(), $id);
        return ApiResponse::success(PollResource::make($result));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $poll = $this->pollService->model()::find($id);

        if (!$poll) {
            return ApiResponse::notFound('Poll not found');
        }

        // Only creator can delete the poll
        if ($poll->user_id !== auth()->id()) {
            return ApiResponse::forbidden('Only the poll creator can delete this poll');
        }

        $this->pollService->delete([$id]);
        return ApiResponse::success();
    }
    public function vote(VoteRequest $request)
    {
        $pollId = $request->input('poll_id');
        $poll = $this->pollService->model()::find($pollId);

        if (!$poll) {
            return ApiResponse::notFound('Poll not found');
        }

        // Check if user can vote on this poll
        if (!$poll->canUserVote(auth()->id())) {
            return ApiResponse::forbidden('You are not assigned to vote on this poll');
        }

        // Check if poll is still open
        if (!$poll->is_open) {
            return ApiResponse::badRequest('This poll is closed');
        }

        $this->pollService->vote(
            pollId: $pollId,
            itemId: $request->input('item_id'),
            userId: auth()->id()
        );
        return ApiResponse::success();
    }
}
