<?php

namespace App\Http\Controllers\api\v1;

use App\Events\CallEvent;
use App\Facades\Livekit;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\Chat;
use App\Rules\FamilyUserRule;
use Illuminate\Http\Request;

class CallsController extends Controller
{
    public function generateToken(Request $request)
    {
        $request->validate([
            'room' => 'required|string|max:255',
            'family_id' => [
                'required',
                new FamilyUserRule
            ]
        ]);
        $room = $request->input('room');
        $familyId = $request->input('family_id');
        $token = Livekit::generateToken("$room-$familyId");
        return ApiResponse::success(['token' => $token]);
    }
    public function accept(Chat $chat)
    {
        broadcast(new CallEvent(auth()->user(), $chat, 'accept'));

        return ApiResponse::success();
    }
    public function endCall(Chat $chat)
    {
        broadcast(new CallEvent(auth()->user(), $chat, 'endCall'));
        return ApiResponse::success();
    }
}
