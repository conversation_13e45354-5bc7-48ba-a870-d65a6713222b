<?php

namespace App\Http\Controllers\api\v1;

use App\Enums\MessageType;
use App\Events\CallEvent;
use App\Events\ChatEvent;
use App\Events\MessageSent;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Messages\SendMessageRequest;
use App\Http\Resources\Chat\MessageResource;
use App\Http\Resources\User\UserResource;
use App\Models\Chat;
use App\Models\User;
use App\Services\CallLogService;
use App\Services\ChatService;
use App\Services\MessageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MessageController extends Controller
{
    public function __construct(protected MessageService $messageService)
    {
    }

    public function store(SendMessageRequest $request)
    {
        $message = $this->messageService->create($request->validated());

        return ApiResponse::success(MessageResource::make($message));
    }
    public function index(Request $request)
    {
        $request->validate(['chat_id' => ['required', 'exists:chats,id']]);
        $messages = $this->messageService->getChatMessages($request->chat_id, auth()->id());
        return ApiResponse::success(MessageResource::collection($messages));
    }
    public function readAll(string $chatId)
    {
        $ids = $this->messageService->readAll(auth()->id(), $chatId);
        broadcast(new ChatEvent(type: 'read', data: [
            'user' => UserResource::make(auth()->user()),
            'messages' => $ids->toArray()
        ], chatId: $chatId));
        return ApiResponse::success();
    }
    public function call(Chat $chat)
    {
        $chat->load(['members']);
        broadcast(new CallEvent(auth()->user(), $chat));

        return ApiResponse::success();
    }
}
