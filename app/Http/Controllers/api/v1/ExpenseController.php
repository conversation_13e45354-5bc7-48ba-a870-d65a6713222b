<?php

namespace App\Http\Controllers\api\v1;

use App\Enums\ExpenseType;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Expenses\CreateExpenseRequest;
use App\Http\Requests\Expenses\UpdateExpenseRequest;
use App\Http\Resources\ExpenseResource;
use App\Rules\FamilyUserRule;
use App\Services\ExpenseService;
use Illuminate\Http\Request;

class ExpenseController extends Controller
{
    public function __construct(protected ExpenseService $expenseService)
    {
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $request->validate([
            'family_id' => ['required', new FamilyUserRule],
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date',
            'type' => 'nullable|in:' . implode(',', array_column(ExpenseType::cases(), 'value'))
        ]);
        $result = $this->expenseService
            ->all(20, ['expenseCategory', 'user'], $request->only('from_date', 'to_date', 'family_id', 'type'));
        return ApiResponse::success(ExpenseResource::collection($result));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateExpenseRequest $request)
    {
        $data = $request->validated();
        $data['user_id'] = auth()->id();
        $result = $this->expenseService->create($data);
        return ApiResponse::success(ExpenseResource::make($result));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id, Request $request)
    {
        $request->validate(['family_id' => ['required', new FamilyUserRule]]);

        $familyId = $request->input('family_id');

        $query = $this->expenseService->model()::with(['expenseCategory', 'user'])
            ->where('family_id', $familyId)
            ->where('id', $id);

        // If not super admin, can only view own expenses
        if (!canViewAllFamilyExpenses($familyId)) {
            $query->where('user_id', auth()->id());
        }

        $expense = $query->first();

        if (!$expense) {
            return ApiResponse::notFound('Expense not found or you do not have permission to view it');
        }

        return ApiResponse::success(ExpenseResource::make($expense));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateExpenseRequest $request, string $id)
    {
        $familyId = $request->input('family_id');

        $query = $this->expenseService->model()::where('id', $id)
            ->where('family_id', $familyId);

        // If not super admin, can only update own expenses
        if (!canViewAllFamilyExpenses($familyId)) {
            $query->where('user_id', auth()->id());
        }

        $expense = $query->first();

        if (!$expense) {
            return ApiResponse::notFound('Expense not found or you do not have permission to update it');
        }

        $data = $request->validated();
        $expense->update($data);
        return ApiResponse::success(ExpenseResource::make($expense));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id, Request $request)
    {
        $request->validate(['family_id' => ['required', new FamilyUserRule]]);

        $familyId = $request->input('family_id');

        $query = $this->expenseService->model()::where('id', $id)
            ->where('family_id', $familyId);

        // If not super admin, can only delete own expenses
        if (!canViewAllFamilyExpenses($familyId)) {
            $query->where('user_id', auth()->id());
        }

        $expense = $query->first();

        if (!$expense) {
            return ApiResponse::notFound('Expense not found or you do not have permission to delete it');
        }

        $expense->delete();
        return ApiResponse::success();
    }
    public function home(Request $request)
    {
        $request->validate(['family_id' => ['required', new FamilyUserRule]]);
        $familyId = $request->input('family_id');

        $result = $this->expenseService->homePage($familyId);

        // Get latest expenses with role-based filtering
        $latest = $this->expenseService->all(10, ['expenseCategory', 'user'], ['family_id' => $familyId]);

        return ApiResponse::success([
            'chart' => $result,
            'latest' => ExpenseResource::collection($latest)
        ]);
    }
}
