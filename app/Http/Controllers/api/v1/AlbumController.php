<?php

namespace App\Http\Controllers\api\v1;

use App\Enums\AlbumType;
use App\Helpers\ApiResponse;
use App\Http\Resources\Documents\AlbumResource;
use App\Models\File;
use App\Models\Album;
use App\Rules\FamilyUserRule;
use Illuminate\Http\Request;
use App\Services\FileService;
use App\Services\AlbumService;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Requests\Album\CreateAlbumRequest;
use App\Http\Requests\Album\UpdateAlbumRequest;
use App\Models\Family;
use Illuminate\Validation\Rule;

class AlbumController extends Controller
{
    public function __construct(
        protected AlbumService $albumService,
    ) {
    }
    /**
     * create new album
     * @param \App\Http\Requests\Album\CreateAlbumRequest $request
     * @return mixed|\Illuminate\Http\JsonResponse
     */
    public function store(CreateAlbumRequest $request)
    {
        $result = $this->albumService->create($request->validated());
        return ApiResponse::success(AlbumResource::make($result));
    }

    public function show($id)
    {
        $album = $this->albumService->model()::with(['gallery', 'user', 'assignments', 'assignedUsers'])->find($id);

        if (!$album) {
            return ApiResponse::notFound('Album not found');
        }

        // Check if user can view this album
        if (!$album->canUserView(auth()->id())) {
            return ApiResponse::forbidden('You do not have permission to view this album');
        }

        return ApiResponse::success(AlbumResource::make($album));
    }

    public function index(Request $request)
    {
        $request->validate([
            'family_id' => ['required', new FamilyUserRule],
            'type' => ['nullable', Rule::in(array_column(AlbumType::cases(), 'value'))],
            'search' => ['nullable', 'string', 'max:50']
        ]);

        $familyId = $request->input('family_id');
        $userId = auth()->id();
        $search = $request->input('search', '');
        $type = $request->input('type');

        // Get albums where user is either creator or assigned
        $albums = $this->albumService->model()::with(['user', 'assignments', 'assignedUsers'])
            ->where('family_id', $familyId)
            ->where('name', 'like', $search . '%')
            ->when($type, fn($query) => $query->where('type', $type))
            ->where(function ($query) use ($userId) {
                $query->where('user_id', $userId) // Creator
                    ->orWhereHas('assignments', function ($q) use ($userId) {
                        $q->where('user_id', $userId); // Assigned user
                    });
            })
            ->paginate(20);

        return ApiResponse::success(AlbumResource::collection($albums));
    }

    /**
     * Update the specified album.
     */
    public function update(UpdateAlbumRequest $request, string $id)
    {
        $album = $this->albumService->model()::find($id);

        if (!$album) {
            return ApiResponse::notFound('Album not found');
        }

        // Only creator can update the album
        if (!$album->canUserEdit(auth()->id())) {
            return ApiResponse::forbidden('Only the album creator can update this album');
        }

        $result = $this->albumService->update($request->validated(), $id);
        return ApiResponse::success(AlbumResource::make($result));
    }

    /**
     * Remove the specified album.
     */
    public function destroy(string $id)
    {
        $album = $this->albumService->model()::find($id);

        if (!$album) {
            return ApiResponse::notFound('Album not found');
        }

        // Only creator can delete the album
        if (!$album->canUserEdit(auth()->id())) {
            return ApiResponse::forbidden('Only the album creator can delete this album');
        }

        $this->albumService->delete([$id]);
        return ApiResponse::success();
    }
}
