<?php

namespace App\Http\Controllers\api\v1;

use App\Events\UserEnteredAreaEvent;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Area\CreateAreaRequest;
use App\Http\Requests\Area\EnterAreaRequest;
use App\Models\Area;
use App\Services\AreaService;
use Illuminate\Http\Request;

class AreaController extends Controller
{
    public function __construct(protected AreaService $areaService)
    {
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $result = $this->areaService->all();
        return ApiResponse::success($result);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateAreaRequest $request)
    {
        $result = $this->areaService->create($request->validated());
        return ApiResponse::success($result);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->areaService->delete([$id]);
        return ApiResponse::success();
    }

    /**
     * Handle user entering an area and broadcast the event
     */
    public function enterArea(EnterAreaRequest $request)
    {
        $validated = $request->validated();
        $user = auth()->user();

        // Find the area
        $area = Area::with('family')->findOrFail($validated['area_id']);

        // Check if user belongs to the area's family
        if (!$user->families->contains($area->family_id)) {
            return ApiResponse::forbidden('You do not have access to this area.');
        }

        // Get metadata from request or set defaults
        $metadata = $validated['metadata'] ?? [];

        // Add timestamp if not provided
        if (!isset($metadata['timestamp'])) {
            $metadata['timestamp'] = now()->toISOString();
        }

        // Broadcast the event
        broadcast(new UserEnteredAreaEvent($user, $area, $metadata));

        return ApiResponse::success([
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
            ],
            'area' => [
                'id' => $area->id,
                'name' => $area->name,
                'family_id' => $area->family_id,
            ],
            'metadata' => $metadata,
        ]);
    }
}
