<?php

namespace App\Http\Controllers\api\v1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\SubTasks\CreateSubTaskRequest;
use App\Http\Requests\SubTasks\UpdateSubTaskRequest;
use App\Http\Requests\Task\SortRequest;
use App\Http\Resources\SubTaskResource;
use App\Rules\FamilyTaskRule;
use App\Rules\FamilyUserRule;
use App\Services\SubTaskService;
use Illuminate\Http\Request;

class SubTaskController extends Controller
{
    public function __construct(protected SubTaskService $subTaskService)
    {
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $request->validate(['task_id' => ['required', new FamilyTaskRule]]);
        $subTasks = $this->subTaskService->orderBy('sort')->all(where: ['task_id' => $request->input('task_id')]);
        return ApiResponse::success($subTasks);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateSubTaskRequest $request)
    {
        $task = $this->subTaskService->create($request->validated());
        return ApiResponse::success(SubTaskResource::make($task));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $task = $this->subTaskService
            ->whereHas('task', fn($q) => $q->whereHas('family', fn($q) => $q->whereHas('users', fn($q) => $q->where('users.id', auth()->id()))))
            ->firstOrfail(where: ['id' => $id]);
        return ApiResponse::success($task);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSubTaskRequest $request, string $id)
    {
        $task = $this->subTaskService
            ->whereHas('task', fn($q) => $q->whereHas('family', fn($q) => $q->whereHas('users', fn($q) => $q->where('users.id', auth()->id()))))
            ->firstOrfail(where: ['id' => $id]);
        $task->update($request->validated());
        return ApiResponse::success($task);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->subTaskService
            ->whereHas('task', fn($q) => $q->whereHas('family', fn($q) => $q->whereHas('users', fn($q) => $q->where('users.id', auth()->id()))))
            ->delete([$id]);
        return ApiResponse::success();
    }
    public function sort(SortRequest $request)
    {
        $this->subTaskService->sort($request->validated());
        return ApiResponse::success();
    }
}
