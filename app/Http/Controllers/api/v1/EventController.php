<?php

namespace App\Http\Controllers\api\v1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Events\CreateEventRequest;
use App\Http\Requests\Events\UpdateEventRequest;
use App\Rules\FamilyUserRule;
use App\Services\EventService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class EventController extends Controller
{
    public function __construct(protected EventService $eventService)
    {
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $request->validate([
            'familyId' => ['required', new FamilyUserRule],
            'date' => ['nullable', 'date'],
            'groupBy' => ['required', 'in:day,week,month']
        ]);
        $events = $this->eventService->getlatest($request->input('familyId'), $request->input('date'), $request->input('groupBy'));

        return ApiResponse::success($events);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateEventRequest $request)
    {
        $data = $request->validated();
        $data['created_by'] = auth()->id();
        $scheduledAt = Carbon::createFromFormat('Y-m-d H:i:s', $data['scheduled_at']);
        preg_match_all('/(\d+)([dhm])/', $data['notify_time'], $matches, PREG_SET_ORDER);
        foreach ($matches as $match) {
            $value = (int) $match[1];
            $unit = $match[2];

            switch ($unit) {
                case 'd':
                    $scheduledAt->subDays($value);
                    break;
                case 'h':
                    $scheduledAt->subHours($value);
                    break;
                case 'm':
                    $scheduledAt->subMinutes($value);
                    break;
            }
        }
        $data['notify_time'] = $scheduledAt;
        $event = $this->eventService->create($data);
        return ApiResponse::success($event);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $event = $this->eventService->getEvent($id, ['family', 'createdBy'], auth()->id());
        return ApiResponse::success($event);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateEventRequest $request, string $id)
    {
        $data = $request->validated();
        if ($request->filled('notify_time') && $request->input('scheduled_at')) {

            $scheduledAt = Carbon::createFromFormat('Y-m-d H:i:s', $data['scheduled_at']);

            preg_match_all('/(\d+)([dhm])/', $data['notify_time'], $matches, PREG_SET_ORDER);
            foreach ($matches as $match) {
                $value = (int) $match[1];
                $unit = $match[2];

                switch ($unit) {
                    case 'd':
                        $scheduledAt->subDays($value);
                        break;
                    case 'h':
                        $scheduledAt->subHours($value);
                        break;
                    case 'm':
                        $scheduledAt->subMinutes($value);
                        break;
                }
            }
            $data['notify_time'] = $scheduledAt;

        }
        $event = $this->eventService->getEvent($id, [], auth()->id());
        $event = $this->eventService->update($data, $id);
        return ApiResponse::success($event);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->eventService->delete([$id]);
        return ApiResponse::success();
    }
}
