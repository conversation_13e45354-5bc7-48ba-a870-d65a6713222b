<?php

namespace App\Http\Controllers\api\v1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\Event\OccasionResource;
use App\Rules\FamilyUserRule;
use App\Services\OccasionService;
use Illuminate\Http\Request;

class OccasionController extends Controller
{
    public function __construct(protected OccasionService $occasionService) {}
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $request->validate(['family_id' => ['required', new FamilyUserRule]]);
        $result = $this->occasionService->all(where: ['family_id' => $request->input('family_id')]);
        return ApiResponse::success(OccasionResource::collection($result));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
