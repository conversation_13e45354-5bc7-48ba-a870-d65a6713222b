<?php

namespace App\Http\Middleware;

use Closure;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;

class SetUserTimezone
{
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        if ($request->user() && $response instanceof JsonResponse) {
            $data = $response->getData(true);
            $userTimeZone = $request->user()->time_zone;

            array_walk_recursive($data, function (&$value, $key) use ($userTimeZone) {
                if ($this->isDate($value) && $key !== 'time_zone') {
                    $value = Carbon::parse($value)
                        ->setTimezone($userTimeZone ?: 'UTC')
                        ->toDateTimeString();
                }
            });

            $response->setData($data);
        }

        return $response;
    }

  
    protected function isDate($value)
    {
        return is_string($value) && strtotime($value) !== false;
    }
}
