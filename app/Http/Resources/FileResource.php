<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'family' => [
                'id' => $this->family->id,
                'name' => $this->family->name,
            ],
            'uploaded_by' => [
                'id' => $this->uploadedBy->id,
                'name' => $this->uploadedBy->name,
            ],
            'album' => $this->album ? [
                'id' => $this->album->id,
                'name' => $this->album->name,
            ] : null,
            'date_add_to_album' => $this->date_add_to_album,
            'file_name' => $this->file_name,
            'file_path' => $this->file_path,
            'file_type' => $this->file_type,
            'size' => $this->size,
            'is_duplicate' => $this->is_duplicate,
            'category' => $this->category,
            'last_accessed_at' => $this->last_accessed_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
