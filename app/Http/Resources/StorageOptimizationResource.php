<?php

namespace App\Http\Resources;

use App\Models\File;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StorageOptimizationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray($request)
    {
        $duplicateFileIds = json_decode($this->duplicate_file_ids, true);
        $unusedFileIds = json_decode($this->unused_file_ids, true);

        $duplicateFiles = FileResource::collection(File::whereIn('id', $duplicateFileIds)->get());
        $unusedFiles = FileResource::collection(File::whereIn('id', $unusedFileIds)->get());

        return [
            'id' => $this->id,
            'family' => [
                'id' => $this->family->id,
                'name' => $this->family->name,
            ],
            'count_duplicate_files' => $this->count_duplicate_files,
            'duplicate_files' => $duplicateFiles,
            'count_unused_files' => $this->count_unused_files,
            'unused_files' => $unusedFiles,
            'total_suggested_cleaning' => $this->total_suggested_cleaning,
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString(),
        ];
    }
}
