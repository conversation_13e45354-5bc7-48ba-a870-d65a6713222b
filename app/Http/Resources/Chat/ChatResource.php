<?php

namespace App\Http\Resources\Chat;

use App\Http\Resources\MediaResource;
use App\Http\Resources\User\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChatResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = parent::toArray($request);
        $data['image'] = MediaResource::make($this->whenLoaded('image'));
        $data['is_archived'] = $this->members->where('id', auth()->id())->first()?->pivot->is_archived;
        $data['members'] = UserResource::collection($this->whenLoaded('members'));
        return $data;
    }
}
