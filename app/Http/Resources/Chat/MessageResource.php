<?php

namespace App\Http\Resources\Chat;

use App\Http\Resources\MediaResource;
use App\Http\Resources\SharedContactResource;
use App\Http\Resources\User\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = parent::toArray($request);
        $data['sender'] = UserResource::make($this->whenLoaded('sender'));
        $data['image'] = MediaResource::make($this->whenLoaded('image'));
        $data['file'] = MediaResource::make($this->whenLoaded('file'));
        $data['voice'] = $this->getFirstMedia('voice') ? $this->getFirstMediaUrl('voice') : null;
        $data['chat'] = ChatResource::make($this->whenLoaded('chat'));
        $data['ready_by'] = UserResource::collection($this->whenLoaded('readyBy'));
        $data['contact'] = SharedContactResource::make($this->whenLoaded('contact'));
        return $data;
    }
}
