<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // return parent::toArray($request);
        return [
            'title' => $this->data['title'],
            'body' => $this->data['body'],
            'created_at' => $this->created_at,
            'sent_at' => $this->created_at->diffForHumans()
        ];

    }
}
