<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExpenseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = parent::toArray($request);
        
        // Add permission flags
        $data['can_edit'] = canEditFamilyExpense($this->resource, $this->family_id);
        $data['can_delete'] = canEditFamilyExpense($this->resource, $this->family_id);
        $data['is_own_expense'] = $this->user_id === auth()->id();
        
        // Include related data
        $data['expense_category'] = $this->whenLoaded('expenseCategory');
        $data['user'] = $this->whenLoaded('user');
        
        return $data;
    }
}
