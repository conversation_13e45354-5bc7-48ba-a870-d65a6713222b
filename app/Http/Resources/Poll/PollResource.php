<?php

namespace App\Http\Resources\Poll;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class PollResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = parent::toArray($request);
        $data['items'] = PollItemResource::collection($this->whenLoaded('items'));
        $data['assigned_users'] = $this->whenLoaded('assignedUsers', function () {
            return $this->assignedUsers->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                ];
            });
        });
        $data['can_vote'] = $this->canUserVote(Auth::id());
        $data['is_creator'] = $this->user_id === Auth::id();
        return $data;
    }
}
