<?php

namespace App\Http\Resources\User;

use App\Http\Resources\Family\FamilyResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = parent::toArray($request);
        $data['image'] = $this->getFirstMediaUrl('image');
        $data['families'] = FamilyResource::collection($this->whenLoaded('families'));
        unset($data['media']);
        return $data;
    }
}
