<?php

namespace App\Http\Resources\Documents;

use App\Http\Resources\MediaResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class AlbumResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = parent::toArray($request);
        $data['type'] = $this->type->name;
        $data['thumbnail'] = $this->getFirstMedia('gallery') ? $this->getFirstMediaUrl('gallery') : null;
        $data['gallery'] = MediaResource::collection($this->whenLoaded('gallery'));
        $data['assigned_users'] = $this->whenLoaded('assignedUsers', function () {
            return $this->assignedUsers->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                ];
            });
        });
        $data['can_view'] = $this->canUserView(Auth::id());
        $data['can_edit'] = $this->canUserEdit(Auth::id());
        $data['is_creator'] = $this->user_id === Auth::id();
        return $data;
    }
}
