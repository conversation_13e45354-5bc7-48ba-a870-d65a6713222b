<?php

namespace App\Http\Resources;

use App\Http\Resources\User\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray($request)
    {
        $data = parent::toArray($request);
        $data['created_by'] = UserResource::make($this->whenLoaded('createdBy'));
        $data['assignees'] = UserResource::collection($this->whenLoaded('assignees'));
        $data['type'] = $this->type->name;
        $data['recurring_pattern'] = ucfirst(strtolower($this->recurring_pattern?->name ?? ''));
        $data['sub_tasks'] = SubTaskResource::collection($this->whenLoaded('subTasks'));
        return $data;
    }
}
