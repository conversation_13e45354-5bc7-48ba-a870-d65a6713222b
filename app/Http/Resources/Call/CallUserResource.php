<?php

namespace App\Http\Resources\Call;

use App\Http\Resources\User\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CallUserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = parent::toArray($request);
        $data['user'] = UserResource::make($this->whenLoaded('user'));
        $data['log'] = CallLogResource::make($this->whenLoaded('log'));
        return $data;
    }
}
