<?php

namespace App\Http\Resources\Call;

use App\Http\Resources\Chat\ChatResource;
use App\Http\Resources\User\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CallLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = parent::toArray($request);
        $data['user'] = UserResource::make($this->whenLoaded('user'));
        $data['call_users'] = CallUserResource::collection($this->whenLoaded('callUsers'));
        $data['duration'] = $this->started_at && $this->closed_at ? $this->closed_at->diffForHumans($this->started_at) : null;
        $data['chat'] = ChatResource::make($this->whenLoaded('chat'));
        return $data;
    }
}
