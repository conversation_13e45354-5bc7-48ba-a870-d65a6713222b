<?php

namespace App\Http\Requests\Events;

use App\Enums\EventType;
use App\Enums\RecurringPattern;
use App\Rules\FamilyUserRule;
use App\Rules\TimeRule;
use Illuminate\Foundation\Http\FormRequest;

class CreateEventRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'family_id' => ['required', new FamilyUserRule],
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'scheduled_at' => ['required', 'date_format:Y-m-d H:i:s'],
            'type' => ['required', "in:" . implode(',', array_column(EventType::cases(), 'value'))],
            'recurring_pattern' => ['required_if:type,recurring', "in:" . implode(',', array_column(RecurringPattern::cases(), 'value'))],
            'recurring_interval' => ['required_if:type,recurring', 'integer'],
            'notify_time' => ['required', new TimeRule],
            'occasion_id' => ['required', 'exists:occasions,id'],
            'location' => ['nullable', 'string', 'max:255']
        ];
    }
}
