<?php

namespace App\Http\Requests\Events;

use App\Enums\EventType;
use App\Enums\RecurringPattern;
use App\Rules\FamilyUserRule;
use App\Rules\TimeRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateEventRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'family_id' => ['nullable', new FamilyUserRule],
            'name' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'scheduled_at' => ['nullable', 'date_format:Y-m-d H:i:s'],
            'type' => ['nullable', "in:" . implode(',', array_column(EventType::cases(), 'value'))],
            'recurring_pattern' => ['nullable', "in:" . implode(',', array_column(RecurringPattern::cases(), 'value'))],
            'recurring_interval' => ['nullable', 'integer'],
            'notify_time' => ['nullable', new TimeRule],
            'occasion_id' => ['required', 'exists:occasions,id'],
            'location' => ['nullable', 'string', 'max:255']
        ];
    }
}
