<?php

namespace App\Http\Requests\Task;

use App\Enums\TaskPriority;
use App\Enums\TaskStatus;
use App\Rules\UserFamilyRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateTaskRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'assigned_to' => ['nullable', new UserFamilyRule],
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'status' => 'nullable|string|in:' . implode(',', array_column(TaskStatus::cases(), 'value')),
            'priority' => 'nullable|string|in:' . implode(',', array_column(TaskPriority::cases(), 'value')),
            'due_date' => 'nullable|date|after_or_equal:today',
            'assignees' => ['nullable', 'array'],
            'assignees.*' => ['required', new UserFamilyRule]
        ];
    }
}
