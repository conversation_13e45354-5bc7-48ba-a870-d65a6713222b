<?php

namespace App\Http\Requests\Task;

use App\Enums\RecurringPattern;
use App\Enums\TaskPriority;
use App\Enums\TaskStatus;
use App\Enums\TaskType;
use App\Rules\FamilyUserRule;
use App\Rules\UserFamilyRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateTaskRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'family_id' => ['required', new FamilyUserRule],
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'nullable|string|in:' . implode(',', array_column(TaskStatus::cases(), 'value')),
            'priority' => 'nullable|string|in:' . implode(',', array_column(TaskPriority::cases(), 'value')),
            'due_date' => 'nullable|date|after_or_equal:today',
            'type' => ['required', Rule::in(array_column(TaskType::cases(), 'value'))],
            'recurring_pattern' => ['required_if:type,recurring', Rule::in(array_column(RecurringPattern::cases(), 'value'))],
            'recurring_interval' => ['required_if:type,recurring,integer'],
            'assignees' => ['nullable', 'array'],
            'assignees.*' => ['required', new UserFamilyRule]
        ];
    }
    public function validated($key = null, $default = null)
    {
        $data = parent::validated();
        $data['created_by'] = auth()->id();
        return $data;
    }
}
