<?php

namespace App\Http\Requests\Album;

use App\Rules\FamilyUserRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateAlbumRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|string|max:255',
            'description' => 'sometimes|string|max:1000',
            'add_files' => 'sometimes|array',
            'add_files.*' => 'exists:files,id',
            'remove_files' => 'sometimes|array',
            'remove_files.*' => 'exists:files,id',
            'family_id' => ['sometimes', new FamilyUserRule],
            'assigned_user_ids' => ['sometimes', 'array'],
            'assigned_user_ids.*' => ['required', 'exists:users,id']
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if ($this->has('assigned_user_ids') && $this->has('family_id')) {
                $familyId = $this->input('family_id');
                $assignedUserIds = $this->input('assigned_user_ids');

                // Check if all assigned users belong to the family
                $familyUserIds = \App\Models\Family::find($familyId)
                        ?->users()
                    ->pluck('users.id')
                    ->toArray() ?? [];

                $invalidUserIds = array_diff($assignedUserIds, $familyUserIds);

                if (!empty($invalidUserIds)) {
                    $validator->errors()->add(
                        'assigned_user_ids',
                        'Some assigned users do not belong to the specified family.'
                    );
                }
            }
        });
    }
}
