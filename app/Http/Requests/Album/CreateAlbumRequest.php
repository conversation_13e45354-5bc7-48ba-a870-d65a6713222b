<?php

namespace App\Http\Requests\Album;

use App\Enums\AlbumType;
use App\Rules\FamilyUserRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateAlbumRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'family_id' => ['required', new FamilyUserRule],
            'name' => 'required|string|max:255',
            'type' => ['required', Rule::in(array_column(AlbumType::cases(), 'value'))],
            'assigned_user_ids' => ['required', 'array', 'min:1'],
            'assigned_user_ids.*' => ['required', 'exists:users,id']
        ];
    }
    public function validated($key = null, $default = null)
    {
        $data = parent::validated($key, $default);
        $data['user_id'] = auth()->id();
        return $data;
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if ($this->has('assigned_user_ids') && $this->has('family_id')) {
                $familyId = $this->input('family_id');
                $assignedUserIds = $this->input('assigned_user_ids');

                // Check if all assigned users belong to the family
                $familyUserIds = \App\Models\Family::find($familyId)
                        ?->users()
                    ->pluck('users.id')
                    ->toArray() ?? [];

                $invalidUserIds = array_diff($assignedUserIds, $familyUserIds);

                if (!empty($invalidUserIds)) {
                    $validator->errors()->add(
                        'assigned_user_ids',
                        'Some assigned users do not belong to the specified family.'
                    );
                }
            }
        });
    }
}
