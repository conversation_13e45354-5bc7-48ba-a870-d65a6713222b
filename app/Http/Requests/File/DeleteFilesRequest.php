<?php

namespace App\Http\Requests\File;

use Illuminate\Foundation\Http\FormRequest;

class DeleteFilesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            // 'family_id' => 'required|exists:families,id',
            'file_ids' => 'required|array|min:1',
            'file_ids.*' => 'integer|exists:files,id',
        ];
    }
}
