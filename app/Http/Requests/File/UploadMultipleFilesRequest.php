<?php

namespace App\Http\Requests\File;

use Illuminate\Foundation\Http\FormRequest;

class UploadMultipleFilesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'family_id' => 'required|exists:families,id',
            'files.*' => 'required|file|max:10240',
            'album_id' => 'nullable|exists:albums,id',
            'category' => 'nullable|string|max:255',
        ];
    }
}
