<?php

namespace App\Http\Requests\SubTasks;

use App\Enums\TaskPriority;
use App\Enums\TaskStatus;
use Illuminate\Foundation\Http\FormRequest;

class UpdateSubTaskRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'content' => ['nullable', 'string', 'max:255'],
            'status' => ['nullable', 'in:' . implode(',', array_column(TaskStatus::cases(), 'value'))],
            'priority' => ['nullable', 'in:' . implode(',', array_column(TaskPriority::cases(), 'value'))],
            'sort' => ['nullable', 'integer']
        ];
    }
}
