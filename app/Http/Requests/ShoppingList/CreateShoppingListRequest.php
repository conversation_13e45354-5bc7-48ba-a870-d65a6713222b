<?php

namespace App\Http\Requests\ShoppingList;

use App\Enums\ShoppingListType;
use App\Rules\FamilyUserRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateShoppingListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'family_id' => ['required', new FamilyUserRule],
            'name' => ['required', 'string', 'max:50'],
            'items' => ['nullable', 'array'],
            'type' => ['required', Rule::in(array_column(ShoppingListType::cases(), 'value'))]
        ];
    }
    public function validated($key = null, $default = null)
    {
        $data = parent::validated();
        $data['created_by'] = $this->user()->id;
        return $data;
    }
}
