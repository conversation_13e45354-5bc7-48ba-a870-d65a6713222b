<?php

namespace App\Http\Requests\Expenses;

use App\Rules\ExpenseCategoryRule;
use App\Rules\FamilyUserRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateExpenseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'family_id' => ['required', new FamilyUserRule],
            'expense_category_id' => ['nullable', new ExpenseCategoryRule],
            'amount' => 'nullable|numeric',
            'description' => 'nullable|string',
            'date' => 'nullable|date'
        ];
    }
}
