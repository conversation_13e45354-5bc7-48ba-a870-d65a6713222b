<?php

namespace App\Http\Requests\Expenses;

use App\Enums\ExpenseType;
use App\Enums\RecurringPattern;
use App\Rules\ExpenseCategoryRule;
use App\Rules\FamilyUserRule;
use Illuminate\Foundation\Http\FormRequest;

class CreateExpenseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'family_id' => ['required', new FamilyUserRule],
            'expense_category_id' => ['required', new ExpenseCategoryRule],
            'amount' => 'required|numeric',
            'description' => 'required|string',
            'date' => 'required|date',
            'type' => 'nullable|in:' . implode(',', array_column(ExpenseType::cases(), 'value')),
            'recurring_type' => 'nullable|in:' . implode(',', array_column(RecurringPattern::cases(), 'value')),
            'recurring_interval' => 'nullable|integer'

        ];
    }
}
