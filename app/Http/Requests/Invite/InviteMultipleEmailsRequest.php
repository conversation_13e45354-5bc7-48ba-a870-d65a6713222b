<?php

namespace App\Http\Requests\Invite;

use App\Rules\FamilyUserRule;
use Illuminate\Foundation\Http\FormRequest;

class InviteMultipleEmailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'emails' => ['required', 'array'],
            'emails.*' => ['required', 'email', 'max:255'],
            'family_id' => ['required', new FamilyUserRule]
        ];
    }
}
