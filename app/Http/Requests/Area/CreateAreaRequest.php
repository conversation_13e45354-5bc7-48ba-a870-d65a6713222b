<?php

namespace App\Http\Requests\Area;

use App\Rules\FamilyUserRule;
use Illuminate\Foundation\Http\FormRequest;

class CreateAreaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'family_id' => ['required', new FamilyUserRule],
            'name' => ['required', 'string', 'max:50'],
            'points' => ['required', 'array']
        ];
    }
}
