<?php

namespace App\Http\Requests\Area;

use Illuminate\Foundation\Http\FormRequest;

class EnterAreaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'area_id' => [
                'required',
                'integer',
                'exists:areas,id',
            ],
            'metadata' => 'sometimes|array',
            'metadata.latitude' => 'sometimes|numeric|between:-90,90',
            'metadata.longitude' => 'sometimes|numeric|between:-180,180',
            'metadata.timestamp' => 'sometimes|date',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'area_id.required' => 'Area ID is required.',
            'area_id.exists' => 'The specified area does not exist or you do not have access to it.',
            'metadata.latitude.between' => 'Latitude must be between -90 and 90.',
            'metadata.longitude.between' => 'Longitude must be between -180 and 180.',
        ];
    }
}
