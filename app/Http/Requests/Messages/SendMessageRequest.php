<?php

namespace App\Http\Requests\Messages;

use App\Enums\MessageType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Log;

class SendMessageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'content' => ['string', 'nullable'],//'required_without:image,file,voice'],
            'type' => 'string|in:' . implode(',', array_column(MessageType::cases(), 'value')),
            'chat_id' => 'required|exists:chats,id',
            'image' => ['required_if:type,image', 'image'],
            'voice' => ['required_if:type,audio'],
            'file' => ['required_if:type,file'],
            'uuid' => ['nullable', 'string'],
            'contact' => ['nullable', 'array'],
            'contact.name' => ['nullable', 'string', 'max:50'],
            'contact_phones' => ['nullable', 'array'],
            'contact_phones.*' => ['nullable', 'string', 'max:50'],
        ];
    }
    public function validated($key = null, $default = null)
    {
        $data = parent::validated();
        $data['user_id'] = auth()->id();
        return $data;
    }
}
