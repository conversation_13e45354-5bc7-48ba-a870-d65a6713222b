<?php

namespace App\Jobs;

use App\Enums\RecurringPattern;
use App\Services\EventService;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class RecurringEventJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $events = app(EventService::class)->getExpiredRecurring();
        foreach ($events as $event) {
            $notifyTime = Carbon::createFromFormat('Y-m-d H:i:s', $event->notify_time);
            $scheduledAt = Carbon::createFromFormat('Y-m-d H:i:s', $event->scheduled_at);
            $newNotifyPeriod = $scheduledAt->diffInMinutes($notifyTime);

            switch ($event->recurring_pattern) {
                case RecurringPattern::DAILY:
                    $event->scheduled_at = $scheduledAt->addDays($event->recurring_interval);
                    break;
                case RecurringPattern::WEEKLY:
                    $event->scheduled_at = $scheduledAt->addWeeks($event->recurring_interval);
                    break;
                case RecurringPattern::MONTHLY:
                    $event->scheduled_at = $scheduledAt->addMonths($event->recurring_interval);
                    break;
                case RecurringPattern::YEARLY:
                    $event->scheduled_at = $scheduledAt->addYears($event->recurring_interval);
                    break;
            }
            $event->notify_time = Carbon::createFromFormat('Y-m-d H:i:s', $event->scheduled_at)->subMinutes($newNotifyPeriod);
            $event->notified_at = null;
            $event->save();
        }
    }
}
