<?php

namespace App\Jobs;

use App\Events\FamilyNotificationEvent;
use App\Services\TaskService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ApproachingTasksJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $tasks = app(TaskService::class)->getApproachingTasks();
        foreach ($tasks as $task) {
            FamilyNotificationEvent::dispatch($task->toArray(), 'task', $task->family_id);
        }
    }
}
