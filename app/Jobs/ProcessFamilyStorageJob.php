<?php

namespace App\Jobs;

use App\Models\File;
use App\Models\Family;
use App\Models\StorageOptimization;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class ProcessFamilyStorageJob implements ShouldQueue
{
    use Queueable;

    public function __construct(public Family $family)
    {
    }

    public function handle(): void
    {
        $family = $this->family;

      // Collect duplicate file identifiers
        $duplicateFileIds = File::where('family_id', $family->id)
            ->where('is_duplicate', true)
            ->pluck('id')
            ->toArray();

      // Collect unused file identifiers
        $unusedFileIds = File::where('family_id', $family->id)
            ->where('last_accessed_at', '<', now()->subMonths(6)) // Not used for 6 months
            ->pluck('id')
            ->toArray();

        // Count the number of duplicate and unused files
        $duplicateFilesCount = count($duplicateFileIds);
        $unusedFilesCount = count($unusedFileIds);

      // Calculate the suggested cleaning volume.
        $suggestedCleaningSize = File::where('family_id', $family->id)
            ->where(function ($query) {
                $query->where('is_duplicate', true)
                      ->orWhere('last_accessed_at', '<', now()->subMonths(6));
            })
            ->sum('size');

        StorageOptimization::updateOrCreate(
            ['family_id' => $family->id],
            [
                'count_duplicate_files' => $duplicateFilesCount,
                'count_unused_files' => $unusedFilesCount,
                'total_suggested_cleaning' => $suggestedCleaningSize,
                'duplicate_file_ids' => json_encode($duplicateFileIds),
                'unused_file_ids' => json_encode($unusedFileIds),
            ]
        );

        $family->update(['is_analysis_in_progress' => false]);
    }
}

