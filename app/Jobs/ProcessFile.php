<?php

namespace App\Jobs;

use AnalyzeFile;
use App\Models\File;
use App\Services\GenerateFileHash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class ProcessFile implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct( protected File $file)
    {
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $hash = $this->generateFileHash();
    
            if ($hash) {
                $this->file->update(['hash' => $hash]);
    
                $this->checkForDuplicates($hash);
            } else {
                Log::error("File not found for hashing: {$this->file->file_path}");
            }
        } catch (\Exception $e) {
            Log::error("Error processing file hash: " . $e->getMessage());
            throw $e;
        }
    }
    
    private function generateFileHash(): ?string
    {
        return GenerateFileHash::generate($this->file->file_path);
    }
    
    private function checkForDuplicates(string $hash): void
    {
        $isDuplicate = File::where('hash', $hash)
            ->where('id', '!=', $this->file->id)
            ->exists();
    
        if ($isDuplicate) {
            $this->file->update(['is_duplicate' => true]);
        }
    }
}
