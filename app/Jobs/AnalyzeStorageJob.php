<?php

namespace App\Jobs;

use App\Models\Family;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class AnalyzeStorageJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Family::chunk(20, function ($families) {
            foreach ($families as $family) {
                ProcessFamilyStorageJob::dispatch($family);
            }
        });
    }
}
