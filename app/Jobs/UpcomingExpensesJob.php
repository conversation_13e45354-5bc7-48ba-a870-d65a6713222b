<?php

namespace App\Jobs;

use App\Events\FamilyNotificationEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class UpcomingExpensesJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $expenses = app(\App\Services\ExpenseService::class)->getUpcomingExpenses();
        foreach ($expenses as $expense) {
            $expense->notified_at = now();
            $expense->save();
            FamilyNotificationEvent::dispatch($expense->toArray(), 'expense', $expense->expenseCategory->family_id);
        }
    }
}
