<?php

namespace App\Jobs;

use App\Events\EventReminder;
use App\Events\FamilyNotificationEvent;
use App\Models\Event;
use App\Services\EventService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class EventReminderJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Event::query()
            ->whereDate('notify_time', '<=', now())
            ->whereNull('notified_at')
            ->chunk(10, function ($chunk) {
                foreach ($chunk as $event) {
                    $event->notified_at = now();
                    $event->save();
                    FamilyNotificationEvent::dispatch($event->toArray(), 'event', $event->family->id);

                }
            });

    }
}
