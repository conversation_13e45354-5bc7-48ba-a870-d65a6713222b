<?php

namespace App\Jobs;

use App\Enums\RecurringPattern;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class RecurringExpensesJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $expenses = app(\App\Services\ExpenseService::class)->getExpiredRecurring();
        foreach ($expenses as $expense) {
            $date = Carbon::createFromFormat('Y-m-d', $expense->date);
            switch ($expense->recurring_pattern) {
                case RecurringPattern::DAILY:
                    $expense->date = $date->addDays($expense->recurring_interval);
                    break;
                case RecurringPattern::WEEKLY:
                    $expense->date = $date->addWeeks($expense->recurring_interval);
                    break;
                case RecurringPattern::MONTHLY:
                    $expense->date = $date->addMonths($expense->recurring_interval);
                    break;
                case RecurringPattern::YEARLY:
                    $expense->date = $date->addYears($expense->recurring_interval);
                    break;
            }
            $expense->notified_at = null;
            $expense->save();
        }
    }
}
