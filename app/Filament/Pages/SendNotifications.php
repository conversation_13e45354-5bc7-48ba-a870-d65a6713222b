<?php

namespace App\Filament\Pages;

use App\Models\User;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

class SendNotifications extends Page implements HasForms
{
    use InteractsWithForms;
    protected static ?string $navigationIcon = 'heroicon-o-bell';

    protected static string $view = 'filament.pages.send-notifications';
    public ?array $data = [];

    public function mount()
    {
        $this->form->fill();
    }
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required(),
                Forms\Components\Textarea::make('body'),
                Forms\Components\Select::make('users')
                    ->multiple()
                    ->options(User::pluck('name', 'id'))
                    ->disabled(fn($get) => $get('all'))
                    ->searchable(),
                Forms\Components\Toggle::make('all')
                    ->live()
            ])
            ->statePath('data');
    }
    public function getFormActions()
    {
        return [
            Forms\Components\Actions\Action::make('submit')
                ->submit('submit')
        ];
    }
    public function submit()
    {
        $data = $this->form->getState();
        if ($data['all']) {

            User::chunk(50, fn($chunk) => Notification::make()
                ->title($data['title'])
                ->body($data['body'])
                ->sendToDatabase($chunk));
        } else {
            $users = User::whereIn('id', $data['users'])->get();
            Notification::make()
                ->title($data['title'])
                ->body($data['body'])
                ->sendToDatabase($users);
        }
        Notification::make()
            ->title('Notification Sent')
            ->success()
            ->send();
    }
}
