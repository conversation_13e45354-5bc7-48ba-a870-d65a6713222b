<?php

namespace App\Filament\Pages;

use App\Models\Family;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Pages\Page;
use Filament\Pages\Tenancy\RegisterTenant;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RegisterFamily extends RegisterTenant
{
    public static function getLabel(): string
    {
        return __('Register Family');
    }
    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')
                ->required()
        ]);
    }
    public static function canView(): bool
    {
        return true;
    }
    protected function handleRegistration(array $data): Model
    {
        $data['created_by'] = auth()->id();
        $family = Family::create($data);
        $family->users()->attach(auth()->user());
        setPermissionsTeamId($family->id);
        $role = Role::firstOrCreate([
            'name' => 'super_admin',
            'family_id' => $family->id,
        ]);
        $permissions = Permission::all();
        $role->syncPermissions($permissions);
        auth()->user()->assignRole($role->id);
        return $family;
    }
}
