<?php

namespace App\Filament\Family\Resources\UserResource\Pages;

use App\Filament\Family\Resources\UserResource;
use App\Services\FamilyInvitaionService;
use App\Services\FamilyService;
use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Forms;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('invite')
                ->label(__("Invite by email"))
                ->form([
                    Forms\Components\TagsInput::make('emails')
                        ->translateLabel()
                        ->required()
                ])->action(fn($data) => $this->invite($data['emails']))
        ];
    }
    protected function invite($users)
    {
        app(FamilyInvitaionService::class)->invite($users, Filament::getTenant());

        Notification::make()
            ->success()
            ->title(__("Invitations sent"))
            ->send();
    }
}
