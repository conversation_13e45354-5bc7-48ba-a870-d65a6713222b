<?php

namespace App\Filament\Family\Resources\UserResource\Pages;

use App\Filament\Family\Resources\UserResource;
use App\Models\Role;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $userData = $data;
        unset($userData['roles']);
        if ($userData['password'] == null) {
            unset($userData['password']);
        }
        $user = parent::handleRecordUpdate($record, $userData);
        $roles = Role::find($data['roles']);
        $user->assignRole($roles);
        return $user;
    }
}
