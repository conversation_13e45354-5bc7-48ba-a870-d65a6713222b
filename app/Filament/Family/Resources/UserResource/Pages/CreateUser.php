<?php

namespace App\Filament\Family\Resources\UserResource\Pages;

use App\Filament\Family\Resources\UserResource;
use App\Models\Role;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;
    protected function handleRecordCreation(array $data): Model
    {
        $userData = $data;
        unset($userData['roles']);
        $user = parent::handleRecordCreation($userData);
        $roles = Role::find($data['roles']);
        $user->assignRole($roles);
        return $user;
    }
}
