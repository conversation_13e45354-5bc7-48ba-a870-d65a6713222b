<?php

namespace App\Filament\Family\Resources;

use App\Enums\RecurringPattern;
use App\Enums\TaskPriority;
use App\Enums\TaskStatus;
use App\Enums\TaskType;
use App\Filament\Family\Resources\TaskResource\Pages;
use App\Filament\Family\Resources\TaskResource\Pages\CreateTask;
use App\Filament\Family\Resources\TaskResource\RelationManagers;
use App\Filament\Family\Resources\TaskResource\RelationManagers\SubTasksRelationManager;
use App\Models\Task;
use App\Tables\Columns\TaskProgress;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TaskResource extends Resource
{
    protected static ?string $model = Task::class;

    protected static ?string $navigationIcon = 'heroicon-o-queue-list';

    public static function getNavigationGroup(): ?string
    {
        return __("Schedule");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->translateLabel()
                    ->required()
                    ->maxLength(191),
                Forms\Components\Select::make('family_id')
                    ->translateLabel()
                    ->relationship('family', 'name')
                    ->label("Family")
                    ->preload()
                    ->live()
                    ->required()
                    ->hiddenOn([CreateTask::class])
                    ->searchable(),
                Forms\Components\Select::make('created_by')
                    ->translateLabel()
                    ->required()
                    ->relationship('createdBy', 'name', modifyQueryUsing: fn($query, $get) => $query->whereHas('families', fn($q) => $q->where('id', $get("family_id"))))
                    ->preload()
                    ->live()
                    ->columnSpanFull()
                    ->hiddenOn([CreateTask::class])
                    ->searchable(),

                Forms\Components\Select::make('assignees')
                    ->translateLabel()
                    ->relationship('assignees', 'name', modifyQueryUsing: fn($query, $get) => $query->whereHas('families', fn($q) => $q->where('families.id', Filament::getId() == 'admin' ?
                        $get('family_id')
                        : Filament::getTenant()->id)))
                    ->multiple()
                    ->preload()
                    ->columnSpanFull()
                    ->searchable(),
                Forms\Components\Select::make('type')
                    ->translateLabel()
                    ->options(TaskType::getAsOptions())
                    ->live()
                    ->required(),
                Forms\Components\Select::make('recurring_pattern')
                    ->translateLabel()
                    ->options(RecurringPattern::getAsOptions())
                    ->hidden(fn($get) => $get('type') != TaskType::recurring->value)
                    ->required(fn($get) => $get('type') == TaskType::recurring->value),
                Forms\Components\TextInput::make('recurring_interval')
                    ->translateLabel()
                    ->required(fn($get) => $get('type') == TaskType::recurring->value)
                    ->hidden(fn($get) => $get('type') != TaskType::recurring->value),

                Forms\Components\Textarea::make('description')
                    ->translateLabel()
                    ->columnSpanFull(),
                Forms\Components\Select::make('status')
                    ->translateLabel()
                    ->options(TaskStatus::getAsOptions())
                    ->default(TaskStatus::TODO->value),
                Forms\Components\Select::make('priority')
                    ->translateLabel()
                    ->options(TaskPriority::getAsOptions())
                    ->default(TaskPriority::medium->value),
                Forms\Components\DateTimePicker::make('due_date')
                    ->translateLabel()
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('createdBy.name')
                    ->sortable()
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('assignedTo.name')
                    ->translateLabel()
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('title')
                    ->translateLabel()
                    ->searchable(),
                TaskProgress::make('Progress')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn($state) => ucfirst(str_replace('_', ' ', $state->value)))
                    ->color(fn($state) => match ($state) {
                        TaskStatus::TODO => 'gray',
                        TaskStatus::IN_PROGRESS => 'info',
                        TaskStatus::DONE => 'success',
                        default => 'gray'
                    }),
                Tables\Columns\TextColumn::make('priority')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn($state) => ucfirst(str_replace('_', ' ', $state->value)))
                    ->color(fn($state) => match ($state) {
                        TaskPriority::low => 'gray',
                        TaskPriority::medium => 'info',
                        TaskPriority::high => 'warning',
                        default => 'gray'
                    }),
                Tables\Columns\TextColumn::make('due_date')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            SubTasksRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTasks::route('/'),
            'create' => Pages\CreateTask::route('/create'),
            'edit' => Pages\EditTask::route('/{record}/edit'),
        ];
    }
}
