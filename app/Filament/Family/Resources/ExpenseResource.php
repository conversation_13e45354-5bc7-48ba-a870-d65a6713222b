<?php

namespace App\Filament\Family\Resources;

use App\Enums\ExpenseType;
use App\Enums\RecurringPattern;
use App\Filament\Family\Resources\ExpenseResource\Pages;
use App\Filament\Family\Resources\ExpenseResource\Pages\CreateExpense;
use App\Filament\Family\Resources\ExpenseResource\RelationManagers;
use App\Filament\Family\Resources\ExpenseResource\RelationManagers\PaymentsRelationManager;
use App\Models\Expense;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ExpenseResource extends Resource
{
    protected static ?string $model = Expense::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    public static function getNavigationGroup(): ?string
    {
        return __("Budget");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('family_id')
                    ->translateLabel()
                    ->label("Family")
                    ->relationship('family', 'name')
                    ->preload()
                    ->live()
                    ->required()
                    ->hiddenOn([CreateExpense::class])
                    ->searchable(),
                Forms\Components\Select::make('user_id')
                    ->label("User")
                    ->translateLabel()
                    ->relationship('user', 'name', modifyQueryUsing: fn($query, $get) => $query->whereHas('families', fn($q) => $q->where('families.id', Filament::getId() == 'admin' ? $get('family_id') : Filament::getTenant()->id)))
                    ->preload()
                    ->searchable(),
                Forms\Components\Select::make('expense_category_id')
                    ->relationship('expenseCategory', 'name', modifyQueryUsing: fn($query, $get) => $query->where('family_id', Filament::getId() == 'admin' ? $get('family_id') : Filament::getTenant()->id))
                    ->preload()
                    ->searchable()
                    ->required(),
                Forms\Components\TextInput::make('amount')
                    ->required()
                    ->numeric(),
                Forms\Components\DatePicker::make('date')
                    ->label('Due date')
                    ->afterOrEqual('today')
                    ->translateLabel(),
                Forms\Components\Select::make('type')
                    ->options(ExpenseType::getAsOptions())
                    ->default(ExpenseType::ONE_TIME->value)
                    ->live()
                    ->required(),
                Forms\Components\Textarea::make('description')
                    ->translateLabel()
                    ->columnSpanFull()
                    ->required(),
                Forms\Components\Select::make('recurring_pattern')
                    ->options(RecurringPattern::getAsOptions())
                    ->required(fn($get) => $get('type') == ExpenseType::RECURRING->value)
                    ->hidden(fn($get) => $get('type') != ExpenseType::RECURRING->value),
                Forms\Components\TextInput::make('recurring_interval')
                    ->required(fn($get) => $get('type') == ExpenseType::RECURRING->value)
                    ->hidden(fn($get) => $get('type') != ExpenseType::RECURRING->value)
                    ->numeric(),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('expenseCategory.name')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Created By')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->translateLabel()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('date')
                    ->label('Due date')
                    ->translateLabel()
                    ->date()
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            PaymentsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExpenses::route('/'),
            'create' => Pages\CreateExpense::route('/create'),
            'edit' => Pages\EditExpense::route('/{record}/edit'),
        ];
    }
}
