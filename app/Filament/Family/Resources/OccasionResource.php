<?php

namespace App\Filament\Family\Resources;

use App\Filament\Family\Resources\OccasionResource\Pages;
use App\Filament\Family\Resources\OccasionResource\Pages\CreateOccasion;
use App\Filament\Family\Resources\OccasionResource\RelationManagers;
use App\Models\Occasion;
use Filament\Forms;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class OccasionResource extends Resource
{
    protected static ?string $model = Occasion::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationGroup(): ?string
    {
        return __("Schedule");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('family_id')
                    ->label('Family')
                    ->translateLabel()
                    ->relationship('family', 'name')
                    ->required()
                    ->preload()
                    ->searchable()
                    ->columnSpanFull()
                    ->hiddenOn([CreateOccasion::class]),
                SpatieMediaLibraryFileUpload::make('image')
                    ->translateLabel()
                    ->collection('image')
                    ->avatar()
                    ->columnSpanFull()
                    ->alignment(Alignment::Center),
                Forms\Components\TextInput::make('name')
                    ->translateLabel()
                    ->columnSpanFull()
                    ->required(),
                Forms\Components\Toggle::make('is_active')
                    ->translateLabel()
                    ->default(true)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                SpatieMediaLibraryImageColumn::make('image')
                    ->translateLabel()
                    ->collection('image')
                    ->circular(),
                Tables\Columns\TextColumn::make('name')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->translateLabel()
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOccasions::route('/'),
            'create' => Pages\CreateOccasion::route('/create'),
            'edit' => Pages\EditOccasion::route('/{record}/edit'),
        ];
    }
}
