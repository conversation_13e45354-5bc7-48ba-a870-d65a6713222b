<?php

namespace App\Filament\Family\Resources;

use App\Enums\EventType;
use App\Enums\RecurringPattern;
use App\Filament\Family\Resources\EventResource\Pages;
use App\Filament\Family\Resources\EventResource\Pages\CreateEvent;
use App\Filament\Family\Resources\EventResource\Pages\ListEvents;
use App\Filament\Family\Resources\EventResource\RelationManagers;
use App\Models\Event;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EventResource extends Resource
{
    protected static ?string $model = Event::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-date-range';

    public static function getNavigationGroup(): ?string
    {
        return __("Schedule");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('family_id')
                    ->translateLabel()
                    ->relationship('family', 'name')
                    ->preload()
                    ->searchable()
                    ->label('Family')
                    ->required()
                    ->live()
                    ->hiddenOn([CreateEvent::class]),
                Forms\Components\Select::make('created_by')
                    ->translateLabel()
                    ->relationship('createdBy', 'name', modifyQueryUsing: fn($query, $get) => $query->whereHas('families', fn($q) => $q->where('id', $get('family_id'))))
                    ->preload()
                    ->searchable()
                    ->hiddenOn([CreateEvent::class]),
                Forms\Components\TextInput::make('name')
                    ->translateLabel()
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('type')
                    ->translateLabel()
                    ->required()
                    ->live()
                    ->options(EventType::getAsOptions())
                    ->default(EventType::ONE_TIME->value),
                Forms\Components\DateTimePicker::make('scheduled_at')
                    ->translateLabel()
                    ->required()
                    ->afterOrEqual('today'),
                Forms\Components\Textarea::make('description')
                    ->translateLabel(),
                Forms\Components\Select::make('occasion_id')
                    ->translateLabel()
                    ->label('Occasion')
                    ->required()
                    ->relationship('occasion', 'name', modifyQueryUsing: fn($query, $get) => $query->where('family_id', Filament::getId() == 'admin' ? $get('family_id') : Filament::getTenant()->id))
                    ->preload()
                    ->searchable(),
                Forms\Components\Select::make('recurring_pattern')
                    ->translateLabel()
                    ->options(RecurringPattern::getAsOptions())
                    ->required(fn($get) => $get('type') == EventType::RECURRING->value)
                    ->hidden(fn($get) => $get('type') != EventType::RECURRING->value),
                Forms\Components\TextInput::make('recurring_interval')
                    ->translateLabel()
                    ->required(fn($get) => $get('type') == EventType::RECURRING->value)
                    ->hidden(fn($get) => $get('type') != EventType::RECURRING->value)
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('family.name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable()
                    ->hiddenOn([ListEvents::class]),
                Tables\Columns\TextColumn::make('name')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->translateLabel()
                    ->formatStateUsing(fn($state) => ucfirst(str_replace('_', ' ', $state->value)))
                    ->searchable(),
                Tables\Columns\TextColumn::make('scheduled_at')
                    ->translateLabel()
                    ->dateTime(),
                Tables\Columns\TextColumn::make('notified_at')
                    ->translateLabel()
                    ->dateTime()
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEvents::route('/'),
            'create' => Pages\CreateEvent::route('/create'),
            'edit' => Pages\EditEvent::route('/{record}/edit'),
        ];
    }
}
