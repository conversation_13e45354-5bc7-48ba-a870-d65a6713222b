<?php

namespace App\Filament\Family\Resources;

use App\Filament\Family\Resources\RoleResource\Pages;
use App\Filament\Family\Resources\RoleResource\RelationManagers;
use App\Models\Role;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    public static function getNavigationGroup(): ?string
    {
        return __("Users and Roles");
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()

                    ->maxLength(255)
                    ->columnSpanFull(),
                Forms\Components\Section::make('Permissions')
                    ->translateLabel()
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->label('Permissions')
                            ->translateLabel()
                            ->schema(fn() => static::getResourcesPermissions())
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }
    public static function getResourcesPermissions()
    {
        $resources = Filament::getResources();
        $items = [];
        foreach ($resources as $item) {
            $parts = explode('\\', $item);
            $resourceName = preg_split('/(?=[A-Z])/', $parts[count($parts) - 1]);
            $modelName = $resourceName[1];
            $items[] = Forms\Components\Section::make($modelName)
                ->schema([
                    Forms\Components\Checkbox::make("viewAny-{$modelName}")
                        ->label("View Any " . strtolower($modelName)),
                    Forms\Components\Checkbox::make("view-{$modelName}")
                        ->label("View " . strtolower($modelName)),
                    Forms\Components\Checkbox::make("create-{$modelName}")
                        ->label("Create " . strtolower($modelName)),
                    Forms\Components\Checkbox::make("update-{$modelName}")
                        ->label("Update " . strtolower($modelName)),
                    Forms\Components\Checkbox::make("delete-{$modelName}")
                        ->label("Delete " . strtolower($modelName)),
                ])->columnSpan(1);
        }
        return $items;
    }
}
