<?php

namespace App\Filament\Family\Resources\TaskResource\RelationManagers;

use App\Enums\TaskPriority;
use App\Enums\TaskStatus;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SubTasksRelationManager extends RelationManager
{
    protected static string $relationship = 'subTasks';

    protected static bool $isLazy = false;
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('content')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('status')
                    ->translateLabel()
                    ->options(TaskStatus::getAsOptions())
                    ->default(TaskStatus::TODO->value),
                Forms\Components\Select::make('priority')
                    ->translateLabel()
                    ->options(TaskPriority::getAsOptions())
                    ->default(TaskPriority::MEDIUM->value),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('content')
            ->columns([
                Tables\Columns\TextColumn::make('content'),
                Tables\Columns\TextColumn::make('status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn($state) => ucfirst(str_replace('_', ' ', $state->value)))
                    ->color(fn($state) => match ($state) {
                        TaskStatus::TODO => 'gray',
                        TaskStatus::IN_PROGRESS => 'info',
                        TaskStatus::DONE => 'success',
                        default => 'gray'
                    }),
                Tables\Columns\TextColumn::make('priority')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn($state) => ucfirst(str_replace('_', ' ', $state->value)))
                    ->color(fn($state) => match ($state) {
                        TaskPriority::LOW => 'gray',
                        TaskPriority::MEDIUM => 'info',
                        TaskPriority::HIGH => 'warning',
                        default => 'gray'
                    }),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
