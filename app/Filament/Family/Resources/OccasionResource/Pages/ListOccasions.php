<?php

namespace App\Filament\Family\Resources\OccasionResource\Pages;

use App\Filament\Family\Resources\OccasionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListOccasions extends ListRecords
{
    protected static string $resource = OccasionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
