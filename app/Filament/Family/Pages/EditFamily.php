<?php

namespace App\Filament\Family\Pages;

use Filament\Forms\Form;
use Filament\Forms;
use Filament\Pages\Page;
use Filament\Pages\Tenancy\EditTenantProfile;

class EditFamily extends EditTenantProfile
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.family.pages.edit-family';

    public static function getLabel(): string
    {
        return __("Family profile");
    }
    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')
        ]);
    }
}
