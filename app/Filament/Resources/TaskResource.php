<?php

namespace App\Filament\Resources;

use App\Filament\Family\Resources\TaskResource as ResourcesTaskResource;
use App\Filament\Resources\TaskResource\Pages;
use App\Filament\Resources\TaskResource\RelationManagers;
use App\Filament\Resources\TaskResource\RelationManagers\AssigneesRelationManager;
use App\Models\Task;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TaskResource extends Resource
{
    protected static ?string $model = Task::class;

    protected static ?string $navigationIcon = 'heroicon-o-queue-list';
    public static function getNavigationGroup(): ?string
    {
        return __("Schedule");
    }
    public static function form(Form $form): Form
    {
        return ResourcesTaskResource::form($form);
    }

    public static function table(Table $table): Table
    {
        return ResourcesTaskResource::table($table);
    }

    public static function getRelations(): array
    {
        return [
            AssigneesRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTasks::route('/'),
            'create' => Pages\CreateTask::route('/create'),
            'edit' => Pages\EditTask::route('/{record}/edit'),
        ];
    }
}
