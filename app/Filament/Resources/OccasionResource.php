<?php

namespace App\Filament\Resources;

use App\Filament\Family\Resources\OccasionResource as ResourcesOccasionResource;
use App\Filament\Resources\OccasionResource\Pages;
use App\Filament\Resources\OccasionResource\RelationManagers;
use App\Models\Occasion;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class OccasionResource extends Resource
{
    protected static ?string $model = Occasion::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    public static function getNavigationGroup(): ?string
    {
        return __("Schedule");
    }
    public static function form(Form $form): Form
    {
        return ResourcesOccasionResource::form($form);
    }

    public static function table(Table $table): Table
    {
        return ResourcesOccasionResource::table($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOccasions::route('/'),
            'create' => Pages\CreateOccasion::route('/create'),
            'edit' => Pages\EditOccasion::route('/{record}/edit'),
        ];
    }
}
