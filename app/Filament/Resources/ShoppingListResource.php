<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShoppingListResource\Pages;
use App\Filament\Resources\ShoppingListResource\RelationManagers;
use App\Filament\Resources\ShoppingListResource\RelationManagers\ShoppingListItemRelationManager;
use App\Models\ShoppingList;
use App\Tables\Columns\ShoppingListProgress;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ShoppingListResource extends Resource
{
    protected static ?string $model = ShoppingList::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-bag';

    public static function getNavigationGroup(): ?string
    {
        return __("Shpping Lists");
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('family_id')
                    ->translateLabel()
                    ->relationship('family', 'name')
                    ->preload()
                    ->searchable()
                    ->label('Family')
                    ->required()
                    ->live(),
                Forms\Components\Select::make('created_by')
                    ->translateLabel()
                    ->relationship('createdBy', 'name', modifyQueryUsing: fn($query, $get) => $query->whereHas('families', fn($q) => $q->where('id', $get('family_id'))))
                    ->preload()
                    ->searchable()
                    ->hidden(),
                Forms\Components\TextInput::make('name')
                    ->translateLabel()
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('family.name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->translateLabel()
                    ->searchable(),
                ShoppingListProgress::make('purchased')
                    ->translateLabel(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ShoppingListItemRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShoppingLists::route('/'),
            'create' => Pages\CreateShoppingList::route('/create'),
            'edit' => Pages\EditShoppingList::route('/{record}/edit'),
        ];
    }
}
