<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Models\Role;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $userData = $data;
        $role = $data['roles'];
        unset($userData['roles'], $userData['family_id']);
        if (isset($userData['password']) || $userData['password'] == null) {
            unset($userData['password']);
        }
        $user = parent::handleRecordUpdate($record, $userData);
        if (isset($data['roles']) && $data['roles'] != null) {

            $role = Role::find($role);
            $user->assignRole($role);
        }
        if (isset($data['family_id']) && $data['family_id'] != null) {

            $user->families()->sync($data['family_id']);
        }

        return $user;
    }
}
