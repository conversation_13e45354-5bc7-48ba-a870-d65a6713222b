<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Models\Role;
use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        $userData = $data;
        $role = $data['roles'];
        unset($userData['roles'], $userData['family_id']);

        $user = parent::handleRecordCreation($userData);
        $role = Role::find($role);
        $user->assignRole($role);
        $user->families()->sync($data['family_id']);
        return $user;
    }
}
