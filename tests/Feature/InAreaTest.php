<?php

namespace Tests\Feature;

use App\Events\UserEnteredAreaEvent;
use App\Models\Area;
use App\Models\Family;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class InAreaTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_enter_area_successfully(): void
    {
        Event::fake();

        // Create a family and user
        $family = Family::factory()->create();
        $user = User::factory()->create();
        $user->families()->attach($family->id);

        // Create an area for the family
        $area = Area::create([
            'family_id' => $family->id,
            'name' => 'Test Area',
            'points' => [
                ['lat' => 40.7128, 'lng' => -74.0060],
                ['lat' => 40.7130, 'lng' => -74.0058],
                ['lat' => 40.7126, 'lng' => -74.0062],
            ],
        ]);

        // Authenticate the user
        Sanctum::actingAs($user);

        // Make the request
        $response = $this->postJson('/api/v1/areas/enter', [
            'area_id' => $area->id,
            'metadata' => [
                'latitude' => 40.7128,
                'longitude' => -74.0060,
            ],
        ]);

        // Assert response
        $response->assertStatus(200)
            ->assertJson([
                'message' => 'success',
                'data' => [
                    'message' => 'Successfully entered area and notified family members.',
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                    ],
                    'area' => [
                        'id' => $area->id,
                        'name' => 'Test Area',
                        'family_id' => $family->id,
                    ],
                ],
            ]);

        // Assert event was dispatched
        Event::assertDispatched(UserEnteredAreaEvent::class, function ($event) use ($user, $area) {
            return $event->user->id === $user->id &&
                $event->area->id === $area->id &&
                isset($event->metadata['latitude']) &&
                isset($event->metadata['longitude']);
        });
    }

    public function test_user_cannot_enter_area_from_different_family(): void
    {
        Event::fake();

        // Create two families
        $family1 = Family::factory()->create();
        $family2 = Family::factory()->create();

        // Create user in family1
        $user = User::factory()->create();
        $user->families()->attach($family1->id);

        // Create area in family2
        $area = Area::create([
            'family_id' => $family2->id,
            'name' => 'Other Family Area',
            'points' => [['lat' => 40.7128, 'lng' => -74.0060]],
        ]);

        // Authenticate the user
        Sanctum::actingAs($user);

        // Make the request
        $response = $this->postJson('/api/v1/areas/enter', [
            'area_id' => $area->id,
        ]);

        // Assert forbidden response
        $response->assertStatus(403)
            ->assertJson([
                'message' => 'You do not have access to this area.',
            ]);

        // Assert event was not dispatched
        Event::assertNotDispatched(UserEnteredAreaEvent::class);
    }

    public function test_enter_area_requires_authentication(): void
    {
        $family = Family::factory()->create();
        $area = Area::create([
            'family_id' => $family->id,
            'name' => 'Test Area',
            'points' => [['lat' => 40.7128, 'lng' => -74.0060]],
        ]);

        // Make request without authentication
        $response = $this->postJson('/api/v1/areas/enter', [
            'area_id' => $area->id,
        ]);

        // Assert unauthorized response
        $response->assertStatus(401);
    }

    public function test_enter_area_validates_required_fields(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Make request without area_id
        $response = $this->postJson('/api/v1/areas/enter', []);

        // Assert validation error
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['area_id']);
    }

    public function test_enter_area_validates_area_exists(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Make request with non-existent area_id
        $response = $this->postJson('/api/v1/areas/enter', [
            'area_id' => 99999,
        ]);

        // Assert validation error
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['area_id']);
    }
}
