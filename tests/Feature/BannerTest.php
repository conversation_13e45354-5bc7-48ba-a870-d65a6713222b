<?php

namespace Tests\Feature;

use App\Models\Banner;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class BannerTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_fetch_active_banners(): void
    {
        // Create test banners
        $activeBanner = Banner::create([
            'title' => 'Active Banner',
            'url' => 'https://example.com',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $inactiveBanner = Banner::create([
            'title' => 'Inactive Banner',
            'url' => 'https://example.com',
            'is_active' => false,
            'sort_order' => 2,
        ]);

        // Test API endpoint
        $response = $this->get('/api/v1/banners');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    [
                        'id' => $activeBanner->id,
                        'title' => 'Active Banner',
                        'url' => 'https://example.com',
                        'sort_order' => 1,
                    ]
                ]
            ]);

        // Should only return active banners
        $this->assertCount(1, $response->json('data'));
    }

    public function test_banners_are_sorted_by_sort_order(): void
    {
        // Create banners with different sort orders
        $banner1 = Banner::create([
            'title' => 'Banner 1',
            'is_active' => true,
            'sort_order' => 3,
        ]);

        $banner2 = Banner::create([
            'title' => 'Banner 2',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $banner3 = Banner::create([
            'title' => 'Banner 3',
            'is_active' => true,
            'sort_order' => 2,
        ]);

        $response = $this->get('/api/v1/banners');

        $response->assertStatus(200);

        $banners = $response->json('data');

        // Should be sorted by sort_order ascending
        $this->assertEquals('Banner 2', $banners[0]['title']);
        $this->assertEquals('Banner 3', $banners[1]['title']);
        $this->assertEquals('Banner 1', $banners[2]['title']);
    }
}
