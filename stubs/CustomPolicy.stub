<?php

namespace {{ namespace }};

use App\Models\User;

class {{policy}}
{
    public function viewAny(User $user)
    {
        return $user->gotRole('viewAny-{{model}}');
    }
    public function view(User $user)
    {
        return $user->gotRole('view-{{model}}');
    }
    public function create(User $user)
    {
        return $user->gotRole('create-{{model}}');
    }
    public function update(User $user)
    {
        return $user->gotRole('update-{{model}}');
    }
    public function delete(User $user)
    {
        return $user->gotRole('delete-{{model}}');
    }
}
