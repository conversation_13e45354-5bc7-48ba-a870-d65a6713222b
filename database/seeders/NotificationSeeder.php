<?php

namespace Database\Seeders;

use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::get();
        for ($i = 0; $i < 60; $i++) {
            Notification::make()
                ->title(fake()->title)
                ->body(fake()->realText(50))
                ->sendToDatabase($users);
        }
    }
}
