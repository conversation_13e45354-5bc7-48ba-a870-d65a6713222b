<?php

namespace Database\Seeders;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        SubscriptionPlan::create([
            'name' => 'Free',
            'storage_limit' => 100,
            'duration_days' => 0,
            'is_default' => true,
            'price' => 0
        ]);
        SubscriptionPlan::create([
            'name' => 'Premium',
            'storage_limit' => 200,
            'duration_days' => 30,
            'is_default' => false,
            'price' => 10
        ]);
    }
}
