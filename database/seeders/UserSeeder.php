<?php

namespace Database\Seeders;

use App\Models\Family;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::create([
            'name' => "admin",
            'email' => '<EMAIL>',
            'password' => 'P@ssw0rd',
            'is_admin' => true
        ]);
        $adminFamily = Family::create(['name' => 'admin']);
        setPermissionsTeamId($adminFamily->id);
        $superAdminRole = Role::create([
            'name' => 'super_admin',
            'family_id' => $adminFamily->id
        ]);
        $admin->assignRole($superAdminRole);
        $adminFamily = $adminFamily->users()->sync($admin);
        $user = User::create([
            'name' => "user",
            'email' => '<EMAIL>',
            'password' => 'P@ssw0rd'
        ]);
        $user1 = User::create([
            'name' => "user1",
            'email' => '<EMAIL>',
            'password' => 'P@ssw0rd'
        ]);
        $family = Family::first();
        $family->users()->sync([
            [
                'user_id' => $user->id,
                'joined_at' => now(),
            ],
            [
                'user_id' => $user1->id,
                'joined_at' => now(),
            ]
        ]);
        setPermissionsTeamId($family->id);
        Artisan::call('shield:generate --all --panel=family');
        Artisan::call('shield:generate --all --panel=admin');
        $role = Role::firstOrCreate([
            'name' => 'super_admin',
            'family_id' => $family->id
        ]);
        $permissions = Permission::pluck('id');
        $role->syncPermissions($permissions);
        $superAdminRole->syncPermissions($permissions);
        $user->assignRole($role);
    }
}
