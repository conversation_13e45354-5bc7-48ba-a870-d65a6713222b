<?php

namespace Database\Factories;

use App\Enums\TaskPriority;
use App\Enums\TaskStatus;
use App\Models\Family;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Task>
 */
class TaskFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'family_id' => Family::inRandomOrder()->value('id'),
            'created_by' => User::inRandomOrder()->value('id'),
            'title' => fake()->sentence(),
            'description' => fake()->paragraph(),
            'status' => fake()->randomElement(array_column(TaskStatus::cases(), 'value')),
            'priority' => fake()->randomElement(array_column(TaskPriority::cases(), 'value')),
            'due_date' => fake()->dateTimeThisYear()
        ];
    }
}
