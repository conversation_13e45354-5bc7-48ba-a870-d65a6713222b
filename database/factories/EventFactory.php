<?php

namespace Database\Factories;

use App\Enums\EventType;
use App\Enums\RecurringPattern;
use App\Models\Family;
use App\Models\Occasion;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Event>
 */
class EventFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'family_id' => Family::inRandomOrder()->value('id'),
            'created_by' => User::inRandomOrder()->value('id'),
            'name' => fake()->sentence(),
            'type' => fake()->randomElement(array_column(EventType::cases(), 'value')),
            'scheduled_at' => fake()->dateTimeBetween('-1 month', '+5 month'),
            'recurring_pattern' => fake()->randomElement(array_column(RecurringPattern::cases(), 'value')),
            'description' => fake()->paragraph(),
            'notify_time' => fake()->dateTimeThisMonth(),
            'occasion_id' => Occasion::inRandomOrder()->value('id'),
        ];
    }
}
