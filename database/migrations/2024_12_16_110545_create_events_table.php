<?php

use App\Enums\EventType;
use App\Enums\RecurringPattern;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Family::class)->constrained();
            $table->foreignIdFor(\App\Models\User::class, 'created_by')->constrained();
            $table->foreignIdFor(\App\Models\Occasion::class)->nullable()->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->enum('type', array_column(EventType::cases(), 'value'));
            $table->datetime('scheduled_at');
            $table->enum('recurring_pattern', array_column(RecurringPattern::cases(), 'value'))->nullable();
            $table->integer('recurring_interval')->nullable();
            $table->text('description')->nullable();
            $table->datetime('notify_time')->nullable();
            $table->datetime('notified_at')->nullable();
            $table->string('location')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
