<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meal_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Family::class)->constrained();
            $table->foreignIdFor(\App\Models\User::class)->constrained();
            $table->string('cuisine_type');
            $table->string('meal_name');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meal_preferences');
    }
};
