<?php

use App\Enums\InvitationStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('family_invitations', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Family::class)->constrained();
            $table->foreignIdFor(\App\Models\User::class, 'invited_by')->constrained()->cascadeOnDelete();
            $table->string('invited_email');
            $table->enum('status', array_column(InvitationStatus::cases(), 'value'))->default('pending');
            $table->string('code')->unique();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('family__invitations');
    }
};
