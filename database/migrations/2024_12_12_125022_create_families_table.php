<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::create('families', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('storage_used')->default(0);
            $table->foreignIdFor(\App\Models\User::class, 'created_by')->nullable()->constrained();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_analysis_in_progress')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('families');
    }
};
