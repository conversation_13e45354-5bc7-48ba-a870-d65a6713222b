<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('storage_optimizations', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Family::class)->constrained();
            $table->integer('count_duplicate_files')->nullable();
            $table->integer('count_unused_files')->nullable();
            $table->decimal('total_suggested_cleaning', 10, 2)->nullable();
            $table->json('duplicate_file_ids')->nullable();
            $table->json('unused_file_ids')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('storage_optimizations');
    }
};
