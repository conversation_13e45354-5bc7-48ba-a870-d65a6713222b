<?php

use App\Enums\SubscriptionStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Family::class)->constrained();
            $table->foreignIdFor(\App\Models\SubscriptionPlan::class)->constrained();
            $table->float('storage_limit');
            $table->datetime('start_date')->default(now());
            $table->datetime('end_date')->nullable();
            $table->enum('status', array_column(SubscriptionStatus::cases(), 'value'))->default('active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
