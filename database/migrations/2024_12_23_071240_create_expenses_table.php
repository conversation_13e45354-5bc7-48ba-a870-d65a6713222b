<?php

use App\Enums\ExpenseType;
use App\Enums\RecurringPattern;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expenses', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Family::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(\App\Models\ExpenseCategory::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(\App\Models\User::class)->constrained()->cascadeOnDelete();
            $table->float('amount');
            $table->date('date');
            $table->text('description');
            $table->boolean('notified_at')->nullable();
            $table->enum('type', array_column(ExpenseType::cases(), 'value'))->nullable();
            $table->enum('recurring_pattern', array_column(RecurringPattern::cases(), 'value'))->nullable();
            $table->integer('recurring_interval')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expenses');
    }
};
